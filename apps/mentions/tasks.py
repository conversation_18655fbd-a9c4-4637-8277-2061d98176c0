"""
Background tasks for mention workflow management.
Handles approval reminders, deadline notifications, and workflow automation.
"""

from celery import shared_task
from django.utils import timezone
from django.db.models import Q, Count
from datetime import datetime, timedelta
from apps.organizations.models import Organization
from apps.mentions.models import Mention, MentionReading
from apps.core.notifications import notify_managers, notify_admins, NotificationManager
from apps.activity_logs.models import ActivityLog
import logging

logger = logging.getLogger(__name__)


@shared_task(bind=True, max_retries=3)
def check_pending_approvals(self):
    """
    Check for mentions pending approval and send reminders to managers
    """
    try:
        current_time = timezone.now()
        
        # Find mentions pending approval for more than 2 hours
        reminder_threshold = current_time - timedelta(hours=2)
        urgent_threshold = current_time - timedelta(hours=24)
        
        organizations = Organization.objects.filter(is_active=True)
        total_reminders = 0
        
        for organization in organizations:
            # Get pending mentions for this organization
            pending_mentions = Mention.objects.filter(
                client__organization=organization,
                status='pending',
                created_at__lt=reminder_threshold
            ).select_related('client', 'created_by')
            
            if not pending_mentions.exists():
                continue
            
            # Separate by urgency
            urgent_mentions = pending_mentions.filter(created_at__lt=urgent_threshold)
            regular_mentions = pending_mentions.exclude(created_at__lt=urgent_threshold)
            
            # Send urgent reminders
            if urgent_mentions.exists():
                _send_approval_reminder(
                    organization=organization,
                    mentions=urgent_mentions,
                    urgency='urgent',
                    threshold_hours=24
                )
                total_reminders += 1
            
            # Send regular reminders
            if regular_mentions.exists():
                _send_approval_reminder(
                    organization=organization,
                    mentions=regular_mentions,
                    urgency='normal',
                    threshold_hours=2
                )
                total_reminders += 1
        
        logger.info(f"Approval reminders sent: {total_reminders}")
        return f"Sent {total_reminders} approval reminders"
        
    except Exception as exc:
        logger.error(f"Approval reminder task failed: {str(exc)}")
        raise self.retry(exc=exc, countdown=900)


@shared_task(bind=True, max_retries=3)
def send_deadline_reminders(self):
    """
    Send deadline reminders for upcoming mentions
    """
    try:
        current_time = timezone.now()
        tomorrow = (current_time + timedelta(days=1)).date()
        
        # Find mentions scheduled for tomorrow that haven't been read
        upcoming_mentions = MentionReading.objects.filter(
            scheduled_date=tomorrow,
            actual_read_time__isnull=True,
            mention__status='scheduled'
        ).select_related('mention', 'mention__client', 'show', 'presenter')
        
        # Group by organization
        org_mentions = {}
        for mention_reading in upcoming_mentions:
            org = mention_reading.mention.client.organization
            if org not in org_mentions:
                org_mentions[org] = []
            org_mentions[org].append(mention_reading)
        
        reminders_sent = 0
        for organization, mentions in org_mentions.items():
            # Send reminder to managers
            notify_managers(
                organization=organization,
                notification_type='deadline_reminder',
                context={
                    'mentions': mentions,
                    'date': tomorrow,
                    'count': len(mentions),
                    'organization': organization
                }
            )
            
            # Send individual reminders to presenters
            presenter_mentions = {}
            for mention_reading in mentions:
                if mention_reading.presenter:
                    if mention_reading.presenter not in presenter_mentions:
                        presenter_mentions[mention_reading.presenter] = []
                    presenter_mentions[mention_reading.presenter].append(mention_reading)
            
            notification_manager = NotificationManager()
            for presenter, presenter_mention_list in presenter_mentions.items():
                notification_manager.send_notification(
                    notification_type='upcoming_mentions',
                    recipients=[presenter.user],
                    context={
                        'mentions': presenter_mention_list,
                        'date': tomorrow,
                        'presenter': presenter,
                        'count': len(presenter_mention_list)
                    },
                    organization=organization
                )
            
            reminders_sent += 1
        
        logger.info(f"Deadline reminders sent to {reminders_sent} organizations")
        return f"Sent deadline reminders to {reminders_sent} organizations"
        
    except Exception as exc:
        logger.error(f"Deadline reminder task failed: {str(exc)}")
        raise self.retry(exc=exc, countdown=3600)


@shared_task(bind=True, max_retries=3)
def check_overdue_mentions(self):
    """
    Check for overdue mentions and alert managers
    """
    try:
        current_time = timezone.now()
        current_date = current_time.date()
        current_time_only = current_time.time()
        
        # Find mentions that are overdue (scheduled time has passed)
        overdue_mentions = MentionReading.objects.filter(
            scheduled_date__lte=current_date,
            actual_read_time__isnull=True,
            mention__status='scheduled'
        ).filter(
            Q(scheduled_date__lt=current_date) |
            Q(scheduled_date=current_date, scheduled_time__lt=current_time_only)
        ).select_related('mention', 'mention__client', 'show', 'presenter')
        
        if not overdue_mentions.exists():
            return "No overdue mentions found"
        
        # Group by organization
        org_overdue = {}
        for mention_reading in overdue_mentions:
            org = mention_reading.mention.client.organization
            if org not in org_overdue:
                org_overdue[org] = []
            org_overdue[org].append(mention_reading)
        
        alerts_sent = 0
        for organization, mentions in org_overdue.items():
            # Calculate how overdue each mention is
            for mention_reading in mentions:
                scheduled_datetime = datetime.combine(
                    mention_reading.scheduled_date,
                    mention_reading.scheduled_time
                )
                overdue_duration = current_time - timezone.make_aware(scheduled_datetime)
                mention_reading.overdue_minutes = int(overdue_duration.total_seconds() / 60)
            
            # Send alert to managers
            notify_managers(
                organization=organization,
                notification_type='mention_overdue',
                context={
                    'mentions': mentions,
                    'count': len(mentions),
                    'organization': organization,
                    'check_time': current_time
                }
            )
            
            # Log the overdue mentions
            ActivityLog.log_activity(
                user=None,
                organization=organization,
                action='overdue_mentions_detected',
                description=f"{len(mentions)} mentions are overdue",
                level='warning',
                metadata={
                    'overdue_count': len(mentions),
                    'mention_ids': [mr.id for mr in mentions]
                }
            )
            
            alerts_sent += 1
        
        logger.info(f"Overdue mention alerts sent to {alerts_sent} organizations")
        return f"Found {overdue_mentions.count()} overdue mentions across {alerts_sent} organizations"
        
    except Exception as exc:
        logger.error(f"Overdue mention check failed: {str(exc)}")
        raise self.retry(exc=exc, countdown=300)


@shared_task(bind=True, max_retries=3)
def detect_scheduling_conflicts(self):
    """
    Detect scheduling conflicts in mentions and alert managers
    """
    try:
        current_time = timezone.now()
        # Check next 7 days for conflicts
        end_date = (current_time + timedelta(days=7)).date()
        
        organizations = Organization.objects.filter(is_active=True)
        conflicts_found = 0
        
        for organization in organizations:
            # Get all scheduled mentions for the next week
            scheduled_mentions = MentionReading.objects.filter(
                mention__client__organization=organization,
                mention__status='scheduled',
                scheduled_date__gte=current_time.date(),
                scheduled_date__lte=end_date
            ).select_related('mention', 'show', 'presenter').order_by('scheduled_date', 'scheduled_time')
            
            # Group by date and check for conflicts
            daily_mentions = {}
            for mention_reading in scheduled_mentions:
                date_key = mention_reading.scheduled_date
                if date_key not in daily_mentions:
                    daily_mentions[date_key] = []
                daily_mentions[date_key].append(mention_reading)
            
            # Check each day for conflicts
            for date, mentions in daily_mentions.items():
                conflicts = _detect_daily_conflicts(mentions)
                
                if conflicts:
                    # Send conflict alert
                    notify_managers(
                        organization=organization,
                        notification_type='conflict_detected',
                        context={
                            'conflicts': conflicts,
                            'date': date,
                            'organization': organization,
                            'conflict_count': len(conflicts)
                        }
                    )
                    
                    # Log conflicts
                    for conflict in conflicts:
                        ActivityLog.log_activity(
                            user=None,
                            organization=organization,
                            action='scheduling_conflict',
                            description=f"Scheduling conflict detected: {conflict['description']}",
                            level='warning',
                            metadata=conflict
                        )
                    
                    conflicts_found += len(conflicts)
        
        logger.info(f"Scheduling conflict detection completed. Found {conflicts_found} conflicts")
        return f"Detected {conflicts_found} scheduling conflicts"
        
    except Exception as exc:
        logger.error(f"Scheduling conflict detection failed: {str(exc)}")
        raise self.retry(exc=exc, countdown=600)


# Helper functions
def _send_approval_reminder(organization, mentions, urgency, threshold_hours):
    """Send approval reminder to managers"""
    try:
        context = {
            'mentions': mentions,
            'count': mentions.count(),
            'urgency': urgency,
            'threshold_hours': threshold_hours,
            'organization': organization
        }
        
        if urgency == 'urgent':
            # Send to admins for urgent items
            notify_admins(
                organization=organization,
                notification_type='approval_reminder',
                context=context
            )
        else:
            # Send to managers for regular items
            notify_managers(
                organization=organization,
                notification_type='approval_reminder',
                context=context
            )
        
        # Log the reminder
        ActivityLog.log_activity(
            user=None,
            organization=organization,
            action='approval_reminder_sent',
            description=f"Approval reminder sent for {mentions.count()} {urgency} mentions",
            level='info' if urgency == 'normal' else 'warning',
            metadata={
                'mention_count': mentions.count(),
                'urgency': urgency,
                'threshold_hours': threshold_hours
            }
        )
        
    except Exception as e:
        logger.error(f"Failed to send approval reminder: {str(e)}")


def _detect_daily_conflicts(mentions):
    """Detect conflicts within a single day's mentions"""
    conflicts = []
    
    # Sort mentions by time
    mentions = sorted(mentions, key=lambda m: m.scheduled_time)
    
    for i, mention1 in enumerate(mentions):
        for mention2 in mentions[i+1:]:
            # Check for time overlap
            if _times_overlap(mention1, mention2):
                conflicts.append({
                    'type': 'time_overlap',
                    'description': f"Time overlap between '{mention1.mention.title}' and '{mention2.mention.title}'",
                    'mention1_id': mention1.id,
                    'mention2_id': mention2.id,
                    'time1': mention1.scheduled_time,
                    'time2': mention2.scheduled_time
                })
            
            # Check for presenter double-booking
            if (mention1.presenter and mention2.presenter and 
                mention1.presenter == mention2.presenter and
                abs((datetime.combine(datetime.today(), mention1.scheduled_time) - 
                     datetime.combine(datetime.today(), mention2.scheduled_time)).total_seconds()) < 300):  # 5 minutes
                conflicts.append({
                    'type': 'presenter_conflict',
                    'description': f"Presenter {mention1.presenter.display_name} double-booked",
                    'mention1_id': mention1.id,
                    'mention2_id': mention2.id,
                    'presenter': mention1.presenter.display_name
                })
    
    return conflicts


def _times_overlap(mention1, mention2):
    """Check if two mentions have overlapping times"""
    # Convert times to datetime for easier calculation
    time1 = datetime.combine(datetime.today(), mention1.scheduled_time)
    time2 = datetime.combine(datetime.today(), mention2.scheduled_time)

    # Add duration to get end times
    end1 = time1 + timedelta(seconds=mention1.mention.duration_seconds)
    end2 = time2 + timedelta(seconds=mention2.mention.duration_seconds)

    # Check for overlap
    return time1 < end2 and time2 < end1


@shared_task(bind=True, max_retries=3)
def auto_resolve_conflicts(self):
    """
    Automatically resolve simple scheduling conflicts where possible
    """
    try:
        current_time = timezone.now()
        # Check next 7 days for conflicts that can be auto-resolved
        end_date = (current_time + timedelta(days=7)).date()

        organizations = Organization.objects.filter(is_active=True)
        resolved_conflicts = 0

        for organization in organizations:
            # Get all scheduled mentions for the next week
            scheduled_mentions = MentionReading.objects.filter(
                mention__client__organization=organization,
                mention__status='scheduled',
                scheduled_date__gte=current_time.date(),
                scheduled_date__lte=end_date
            ).select_related('mention', 'show', 'presenter').order_by('scheduled_date', 'scheduled_time')

            # Group by date and resolve conflicts
            daily_mentions = {}
            for mention_reading in scheduled_mentions:
                date_key = mention_reading.scheduled_date
                if date_key not in daily_mentions:
                    daily_mentions[date_key] = []
                daily_mentions[date_key].append(mention_reading)

            # Resolve conflicts for each day
            for date, mentions in daily_mentions.items():
                resolved_count = _auto_resolve_daily_conflicts(mentions, organization)
                resolved_conflicts += resolved_count

        logger.info(f"Auto-resolved {resolved_conflicts} conflicts")
        return f"Auto-resolved {resolved_conflicts} conflicts"

    except Exception as exc:
        logger.error(f"Auto conflict resolution failed: {str(exc)}")
        raise self.retry(exc=exc, countdown=1800)


@shared_task(bind=True, max_retries=3)
def generate_conflict_reports(self):
    """
    Generate detailed conflict reports for managers
    """
    try:
        current_time = timezone.now()
        # Generate weekly conflict reports
        week_start = current_time.date()
        week_end = week_start + timedelta(days=7)

        organizations = Organization.objects.filter(is_active=True)
        reports_generated = 0

        for organization in organizations:
            # Gather conflict data for the week
            conflict_data = _gather_conflict_data(organization, week_start, week_end)

            if conflict_data['total_conflicts'] > 0:
                # Send detailed conflict report to managers
                notify_managers(
                    organization=organization,
                    notification_type='conflict_report',
                    context={
                        'conflict_data': conflict_data,
                        'week_start': week_start,
                        'week_end': week_end,
                        'organization': organization
                    }
                )

                reports_generated += 1

        logger.info(f"Generated {reports_generated} conflict reports")
        return f"Generated {reports_generated} conflict reports"

    except Exception as exc:
        logger.error(f"Conflict report generation failed: {str(exc)}")
        raise self.retry(exc=exc, countdown=3600)


@shared_task(bind=True, max_retries=3)
def monitor_resolution_effectiveness(self):
    """
    Monitor the effectiveness of conflict resolution and suggest improvements
    """
    try:
        current_time = timezone.now()
        # Analyze last 30 days of conflict resolution
        analysis_start = current_time - timedelta(days=30)

        organizations = Organization.objects.filter(is_active=True)
        analysis_results = []

        for organization in organizations:
            # Get conflict resolution data
            resolution_data = _analyze_resolution_effectiveness(organization, analysis_start, current_time)

            if resolution_data['needs_attention']:
                # Send improvement suggestions to admins
                notify_admins(
                    organization=organization,
                    notification_type='resolution_analysis',
                    context={
                        'analysis': resolution_data,
                        'organization': organization,
                        'period_start': analysis_start,
                        'period_end': current_time
                    }
                )

                analysis_results.append(organization.name)

        logger.info(f"Resolution effectiveness analysis completed for {len(analysis_results)} organizations")
        return f"Analyzed resolution effectiveness for {len(analysis_results)} organizations needing attention"

    except Exception as exc:
        logger.error(f"Resolution effectiveness monitoring failed: {str(exc)}")
        raise self.retry(exc=exc, countdown=7200)


# Enhanced helper functions
def _auto_resolve_daily_conflicts(mentions, organization):
    """Automatically resolve conflicts within a single day's mentions"""
    resolved_count = 0

    # Sort mentions by time
    mentions = sorted(mentions, key=lambda m: m.scheduled_time)

    for i, mention1 in enumerate(mentions):
        for j, mention2 in enumerate(mentions[i+1:], i+1):
            # Check for time overlap
            if _times_overlap(mention1, mention2):
                # Try to auto-resolve by moving the later mention
                if _can_auto_resolve(mention1, mention2):
                    new_time = _find_next_available_slot(mention2, mentions)
                    if new_time:
                        # Update the mention time
                        mention2.scheduled_time = new_time
                        mention2.save()

                        # Log the auto-resolution
                        ActivityLog.log_activity(
                            user=None,
                            organization=organization,
                            action='conflict_auto_resolved',
                            description=f"Auto-resolved conflict: moved '{mention2.mention.title}' to {new_time}",
                            level='info',
                            content_object=mention2,
                            metadata={
                                'original_time': str(mention2.scheduled_time),
                                'new_time': str(new_time),
                                'conflicted_with': mention1.id
                            }
                        )

                        resolved_count += 1

    return resolved_count


def _can_auto_resolve(mention1, mention2):
    """Check if a conflict can be automatically resolved"""
    # Only auto-resolve if:
    # 1. One mention has lower priority
    # 2. The conflict is minor (less than 5 minutes overlap)
    # 3. There's available time nearby

    if mention1.mention.priority > mention2.mention.priority:
        return True

    # Check overlap duration
    time1 = datetime.combine(datetime.today(), mention1.scheduled_time)
    time2 = datetime.combine(datetime.today(), mention2.scheduled_time)
    overlap_seconds = min(
        (time1 + timedelta(seconds=mention1.mention.duration_seconds) - time2).total_seconds(),
        (time2 + timedelta(seconds=mention2.mention.duration_seconds) - time1).total_seconds()
    )

    return overlap_seconds < 300  # Less than 5 minutes


def _find_next_available_slot(mention, all_mentions):
    """Find the next available time slot for a mention"""
    from datetime import time

    # Start looking 5 minutes after the original time
    current_time = datetime.combine(datetime.today(), mention.scheduled_time)
    search_time = current_time + timedelta(minutes=5)

    # Search for up to 2 hours
    max_search_time = current_time + timedelta(hours=2)

    while search_time <= max_search_time:
        new_time = search_time.time()

        # Check if this time conflicts with any existing mentions
        conflicts = False
        for other_mention in all_mentions:
            if other_mention.id == mention.id:
                continue

            # Create temporary mention with new time to check conflicts
            temp_mention = type('TempMention', (), {
                'scheduled_time': new_time,
                'mention': mention.mention
            })()

            if _times_overlap(temp_mention, other_mention):
                conflicts = True
                break

        if not conflicts:
            return new_time

        # Move to next 30-second slot
        search_time += timedelta(seconds=30)

    return None


def _gather_conflict_data(organization, start_date, end_date):
    """Gather comprehensive conflict data for reporting"""
    # Get all conflicts in the date range
    conflicts = ActivityLog.objects.filter(
        organization=organization,
        action__in=['scheduling_conflict', 'conflict_auto_resolved'],
        created_at__date__gte=start_date,
        created_at__date__lte=end_date
    )

    conflict_data = {
        'total_conflicts': conflicts.count(),
        'auto_resolved': conflicts.filter(action='conflict_auto_resolved').count(),
        'manual_resolution_needed': conflicts.filter(action='scheduling_conflict').count(),
        'conflict_types': {},
        'peak_conflict_times': [],
        'most_conflicted_shows': [],
    }

    # Analyze conflict types
    for conflict in conflicts:
        conflict_type = conflict.metadata.get('type', 'unknown')
        if conflict_type not in conflict_data['conflict_types']:
            conflict_data['conflict_types'][conflict_type] = 0
        conflict_data['conflict_types'][conflict_type] += 1

    return conflict_data


def _analyze_resolution_effectiveness(organization, start_date, end_date):
    """Analyze the effectiveness of conflict resolution"""
    # Get resolution data
    resolutions = ActivityLog.objects.filter(
        organization=organization,
        action__in=['conflict_auto_resolved', 'conflict_manual_resolved'],
        created_at__gte=start_date,
        created_at__lte=end_date
    )

    total_resolutions = resolutions.count()
    auto_resolutions = resolutions.filter(action='conflict_auto_resolved').count()

    analysis = {
        'total_resolutions': total_resolutions,
        'auto_resolution_rate': (auto_resolutions / total_resolutions * 100) if total_resolutions > 0 else 0,
        'needs_attention': False,
        'recommendations': []
    }

    # Determine if attention is needed
    if analysis['auto_resolution_rate'] < 50 and total_resolutions > 10:
        analysis['needs_attention'] = True
        analysis['recommendations'].append("Consider reviewing scheduling practices to reduce manual conflicts")

    if total_resolutions > 50:
        analysis['needs_attention'] = True
        analysis['recommendations'].append("High conflict volume detected - review scheduling workflow")

    return analysis
