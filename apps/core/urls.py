from django.urls import path
from . import views
from . import test_error_views

app_name = 'core'

urlpatterns = [
    # Dashboard
    path('', views.dashboard, name='dashboard'),
    path('dashboard/', views.dashboard, name='dashboard_alt'),

    # Debug view (temporary)
    path('debug/', views.debug_session, name='debug_session'),

    # Presenter Dashboard Access
    path('presenter-dashboard/', views.my_presenter_dashboard, name='my_presenter_dashboard'),
    path('presenter-login/', views.presenter_login_redirect, name='presenter_login_redirect'),
    path('presenter-signin/', views.presenter_login_page, name='presenter_login_page'),

    # Client URLs
    path('clients/', views.ClientListView.as_view(), name='client_list'),
    path('clients/<int:pk>/', views.ClientDetailView.as_view(), name='client_detail'),
    path('clients/create/', views.ClientCreateView.as_view(), name='client_create'),
    path('clients/<int:pk>/edit/', views.ClientUpdateView.as_view(), name='client_edit'),
    path('clients/<int:pk>/delete/', views.ClientDeleteView.as_view(), name='client_delete'),

    # Industry management
    path('industries/', views.IndustryListView.as_view(), name='industry_list'),
    path('industries/<int:pk>/', views.IndustryDetailView.as_view(), name='industry_detail'),
    path('industries/create/', views.IndustryCreateView.as_view(), name='industry_create'),
    path('industries/<int:pk>/edit/', views.IndustryUpdateView.as_view(), name='industry_edit'),
    path('industries/<int:pk>/delete/', views.IndustryDeleteView.as_view(), name='industry_delete'),

    # Presenter URLs
    path('presenters/', views.PresenterListView.as_view(), name='presenter_list'),
    path('presenters/<int:pk>/', views.PresenterDetailView.as_view(), name='presenter_detail'),
    path('presenters/<int:pk>/dashboard/', views.presenter_dashboard, name='presenter_dashboard'),
    path('presenters/<int:pk>/calendar/', views.presenter_calendar, name='presenter_calendar'),
    path('presenters/<int:pk>/edit/', views.PresenterUpdateView.as_view(), name='presenter_edit'),
    path('presenters/<int:pk>/delete/', views.PresenterDeleteView.as_view(), name='presenter_delete'),

    # Live Show Page
    path('live-show/', views.live_show_page, name='live_show_page'),
    path('live-show/mark-read/<int:reading_id>/', views.mark_mention_read, name='mark_mention_read'),
    path('live-show/skip/<int:reading_id>/', views.skip_mention, name='skip_mention'),
    path('live-show/end-show/', views.end_show, name='end_show'),
    path('live-show/reorder-mentions/', views.reorder_mentions, name='reorder_mentions'),
    path('live-show/weather/', views.get_weather_data, name='get_weather_data'),

    # Live Show Monitoring (Admin Only)
    path('live-monitor/', views.live_show_monitor, name='live_show_monitor'),
    path('live-monitor/api/', views.live_show_monitor_api, name='live_show_monitor_api'),
    path('system-performance/', views.system_performance_dashboard, name='system_performance_dashboard'),
    path('system-performance/api/', views.system_performance_api, name='system_performance_api'),

    # Notification API endpoints
    path('api/notifications/sync/', views.notifications_sync, name='notifications_sync'),
    path('api/notifications/recent/', views.notifications_recent, name='notifications_recent'),
    path('api/notifications/unread-count/', views.notifications_unread_count, name='notifications_unread_count'),
    path('api/notifications/<int:notification_id>/mark-read/', views.notifications_mark_read, name='notifications_mark_read'),
    path('api/notifications/mark-all-read/', views.notifications_mark_all_read, name='notifications_mark_all_read'),

    # Error page testing (DEBUG mode only)
    path('test-errors/', test_error_views.error_test_index, name='test_error_index'),
    path('test-errors/404/', test_error_views.test_404_view, name='test_404'),
    path('test-errors/500/', test_error_views.test_500_view, name='test_500'),
    path('test-errors/403/', test_error_views.test_403_view, name='test_403'),
    path('test-errors/400/', test_error_views.test_400_view, name='test_400'),
    path('test-errors/csrf/', test_error_views.test_csrf_view, name='test_csrf'),
    path('test-errors/maintenance/', test_error_views.test_maintenance_view, name='test_maintenance'),
]
