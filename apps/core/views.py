from django.shortcuts import render, get_object_or_404, redirect
from django.http import HttpResponse
from django.contrib.auth.decorators import login_required
from apps.core.decorators import require_permission, presenter_only, admin_or_manager_required, owner_or_admin_required
from django.contrib import messages
from django.http import JsonResponse
from django.views.generic import ListView, DetailView, CreateView, UpdateView, DeleteView
from django.contrib.auth.mixins import LoginRequiredMixin
from django.urls import reverse_lazy, reverse
from django.db import models
from django.db.models import Count, Q, Avg, F, Case, When, IntegerField
from django.utils import timezone
from django.utils.decorators import method_decorator
from datetime import datetime, timedelta
import json

from .models import Client, Presenter, PresenterAvailability, PresenterSkill, PresenterNote, Industry
from apps.mentions.models import Mention, MentionReading
from apps.shows.models import Show
from apps.organizations.middleware import get_current_organization, OrganizationDataFilterMixin
from .services.weather_service import get_organization_weather, WeatherService
from .forms import ClientForm, IndustryForm


def locked_out(request):
    """View for when user is locked out due to too many failed login attempts"""
    username = request.GET.get('username', '')
    context = {
        'username': username,
        'title': 'Account Temporarily Locked',
    }
    return render(request, 'core/locked_out.html', context)


def debug_session(request):
    """Debug view to check session state"""
    from apps.organizations.models import OrganizationMembership
    from apps.core.models import Presenter
    from apps.organizations.middleware import get_current_membership, user_has_permission

    debug_info = []
    debug_info.append(f"User authenticated: {request.user.is_authenticated}")

    if request.user.is_authenticated:
        debug_info.append(f"Username: {request.user.username}")
        debug_info.append(f"Name: {request.user.first_name} {request.user.last_name}")
        debug_info.append(f"Email: {request.user.email}")

        # Check session
        current_org_id = request.session.get('current_organization_id')
        debug_info.append(f"Current org ID in session: {current_org_id}")

        # Check memberships
        memberships = OrganizationMembership.objects.filter(user=request.user, is_active=True)
        debug_info.append(f"Active memberships: {memberships.count()}")

        for membership in memberships:
            debug_info.append(f"  - {membership.organization.name} (Role: {membership.role}, Default: {membership.is_default})")

        # Check current membership and permissions
        current_membership = get_current_membership(request)
        if current_membership:
            debug_info.append(f"Current membership: {current_membership.organization.name} - {current_membership.role}")
            debug_info.append(f"Has manage_api_keys: {user_has_permission(request, 'manage_api_keys')}")
            debug_info.append(f"Has manage_settings: {user_has_permission(request, 'manage_settings')}")
            debug_info.append(f"Has admin_access: {user_has_permission(request, 'admin_access')}")
        else:
            debug_info.append("No current membership found")

        # Check presenter profile
        try:
            presenter = Presenter.objects.get(user=request.user)
            debug_info.append(f"Presenter profile: {presenter.display_name} in {presenter.organization.name}")
        except Presenter.DoesNotExist:
            debug_info.append("No presenter profile found")

        # Check all presenters
        all_presenters = Presenter.objects.all()
        debug_info.append(f"Total presenters in system: {all_presenters.count()}")
        for p in all_presenters:
            debug_info.append(f"  - {p.display_name} in {p.organization.name} (Active: {p.is_active})")

    return HttpResponse("<br>".join(debug_info))


@login_required
def dashboard(request):
    """Enhanced dashboard view with comprehensive statistics and charts"""
    from apps.organizations.middleware import get_current_membership
    from apps.activity_logs.models import ActivityLog
    from apps.mentions.models import RecurringMention
    from django.db.models import Q, Count, Avg, Sum
    from django.contrib.contenttypes.models import ContentType

    # Check if user is a presenter and redirect to presenter dashboard
    current_membership = get_current_membership(request)
    if current_membership and current_membership.role == 'presenter':
        return redirect('core:my_presenter_dashboard')

    # Check if user is a news reader and redirect to news reader dashboard
    if current_membership and current_membership.role == 'news_reader':
        return redirect('news_reader:dashboard')

    today = datetime.now().date()
    current_datetime = timezone.now()
    last_month = today - timedelta(days=30)
    last_week = today - timedelta(days=7)
    yesterday = today - timedelta(days=1)
    last_30_days = current_datetime - timedelta(days=30)
    last_7_days = current_datetime - timedelta(days=7)

    # Get current organization
    current_org = get_current_organization(request)
    if not current_org:
        return redirect('organizations:list')

    # Calculate comprehensive statistics filtered by organization
    stats = {
        'total_mentions': Mention.objects.filter(client__organization=current_org).count(),
        'pending_mentions': Mention.objects.filter(client__organization=current_org, status='pending').count(),
        'active_clients': Client.objects.filter(organization=current_org, is_active=True).count(),
        'scheduled_today': MentionReading.objects.filter(
            mention__client__organization=current_org,
            mention__status='scheduled',
            scheduled_date=today
        ).count(),
        'shows_today': MentionReading.objects.filter(
            mention__client__organization=current_org,
            mention__status='scheduled',
            scheduled_date=today
        ).values('show').distinct().count(),
        'completed_today': MentionReading.objects.filter(
            mention__client__organization=current_org,
            mention__status='scheduled',
            scheduled_date=today,
            actual_read_time__isnull=False
        ).count(),
        'active_presenters': Presenter.objects.filter(organization=current_org, is_active=True).count(),
        'active_shows': Show.objects.filter(organization=current_org, is_active=True).count(),

        # Enhanced performance metrics
        'total_shows': Show.objects.filter(organization=current_org).count(),
        'active_recurring_mentions': RecurringMention.objects.filter(
            client__organization=current_org,
            status='active'
        ).count(),
        'completed_mentions_30_days': MentionReading.objects.filter(
            mention__client__organization=current_org,
            actual_read_time__isnull=False,
            actual_read_time__gte=last_30_days
        ).count(),
        'avg_mentions_per_show': MentionReading.objects.filter(
            mention__client__organization=current_org,
            actual_read_time__isnull=False,
            actual_read_time__gte=last_30_days
        ).values('show').annotate(count=Count('id')).aggregate(avg=Avg('count'))['avg'] or 0,
    }

    # Calculate growth percentages and historical data
    last_month_mentions = Mention.objects.filter(
        client__organization=current_org,
        created_at__date__lt=last_month
    ).count()
    stats['mentions_growth'] = calculate_growth(stats['total_mentions'], last_month_mentions)

    last_week_pending = Mention.objects.filter(
        client__organization=current_org,
        status='pending',
        created_at__date__lt=last_week
    ).count()
    stats['pending_growth'] = calculate_growth(stats['pending_mentions'], last_week_pending)

    last_month_clients = Client.objects.filter(
        organization=current_org,
        is_active=True,
        created_at__date__lt=last_month
    ).count()
    stats['clients_growth'] = calculate_growth(stats['active_clients'], last_month_clients)

    # Yesterday's completion data for comparison
    yesterday_scheduled = MentionReading.objects.filter(
        mention__client__organization=current_org,
        mention__status='scheduled',
        scheduled_date=yesterday
    ).count()
    yesterday_completed = MentionReading.objects.filter(
        mention__client__organization=current_org,
        mention__status='scheduled',
        scheduled_date=yesterday,
        actual_read_time__isnull=False
    ).count()

    # Calculate completion rate
    stats['completion_rate'] = 0
    if stats['scheduled_today'] > 0:
        stats['completion_rate'] = round((stats['completed_today'] / stats['scheduled_today']) * 100, 1)

    # Store historical data for display
    stats['last_month_mentions'] = last_month_mentions
    stats['last_week_pending'] = last_week_pending
    stats['last_month_clients'] = last_month_clients
    stats['yesterday_scheduled'] = yesterday_scheduled
    stats['yesterday_completed'] = yesterday_completed

    # Industry Analytics with color coding
    industry_analytics = []
    industries = Industry.objects.filter(organization=current_org, is_active=True)
    for industry in industries:
        mention_count = Mention.objects.filter(
            client__organization=current_org,
            client__industry=industry.code,
            created_at__gte=last_30_days
        ).count()

        active_mentions = Mention.objects.filter(
            client__organization=current_org,
            client__industry=industry.code,
            status__in=['pending', 'scheduled']
        ).count()

        industry_analytics.append({
            'industry': industry,
            'mention_count': mention_count,
            'active_mentions': active_mentions,
            'color': industry.color,
            'percentage': 0  # Will be calculated after getting total
        })

    # Calculate percentages
    total_industry_mentions = sum(item['mention_count'] for item in industry_analytics)
    if total_industry_mentions > 0:
        for item in industry_analytics:
            item['percentage'] = round((item['mention_count'] / total_industry_mentions) * 100, 1)

    # Sort by mention count
    industry_analytics.sort(key=lambda x: x['mention_count'], reverse=True)

    # Show Management Updates (recent changes)
    show_content_type = ContentType.objects.get_for_model(Show)
    show_updates = ActivityLog.objects.filter(
        organization=current_org,
        content_type=show_content_type,
        created_at__gte=last_7_days
    ).select_related('user', 'content_type').order_by('-created_at')[:10]

    # Recurring Schedule Status
    recurring_stats = {
        'active_count': RecurringMention.objects.filter(
            client__organization=current_org,
            status='active'
        ).count(),
        'paused_count': RecurringMention.objects.filter(
            client__organization=current_org,
            status='paused'
        ).count(),
        'ended_recently': RecurringMention.objects.filter(
            client__organization=current_org,
            status__in=['ended', 'finished'],
            updated_at__gte=last_7_days
        ).count(),
        'total_aired_30_days': RecurringMention.objects.filter(
            client__organization=current_org,
            status='active'
        ).aggregate(total=Sum('total_aired'))['total'] or 0,
    }

    # Get today's schedule with enhanced data
    todays_schedule = MentionReading.objects.filter(
        mention__client__organization=current_org,
        mention__status='scheduled',
        scheduled_date=today
    ).select_related('mention', 'show', 'presenter', 'mention__client').order_by('scheduled_time')

    # Get pending mentions for approval with priority sorting
    pending_mentions = Mention.objects.filter(
        client__organization=current_org,
        status='pending'
    ).select_related('client', 'created_by').order_by('priority', '-created_at')[:6]

    # Mention Activity Tracking
    mention_activity = {
        'created_today': Mention.objects.filter(
            client__organization=current_org,
            created_at__date=today
        ).count(),
        'edited_today': ActivityLog.objects.filter(
            organization=current_org,
            content_type=ContentType.objects.get_for_model(Mention),
            action='update',
            created_at__date=today
        ).count(),
        'approved_today': Mention.objects.filter(
            client__organization=current_org,
            approved_at__date=today
        ).count(),
        'completed_today': MentionReading.objects.filter(
            mention__client__organization=current_org,
            actual_read_time__date=today
        ).count(),
        'status_changes_7_days': ActivityLog.objects.filter(
            organization=current_org,
            content_type=ContentType.objects.get_for_model(Mention),
            created_at__gte=last_7_days,
            description__icontains='status'
        ).count(),
    }

    # Comprehensive Recent Activity Feed
    recent_activity_feed = []

    # Get recent mention readings
    recent_readings = MentionReading.objects.filter(
        mention__client__organization=current_org,
        actual_read_time__isnull=False,
        actual_read_time__gte=last_7_days
    ).select_related('mention', 'show', 'presenter', 'mention__client').order_by('-actual_read_time')[:10]

    for reading in recent_readings:
        recent_activity_feed.append({
            'type': 'mention_completed',
            'timestamp': reading.actual_read_time,
            'user': reading.presenter.display_name if reading.presenter else 'Unknown',
            'description': f'Completed mention "{reading.mention.title}" on {reading.show.name}',
            'object': reading.mention,
            'icon': 'fa-check-circle',
            'color': 'text-green-600'
        })

    # Get recent activity logs
    recent_logs = ActivityLog.objects.filter(
        organization=current_org,
        created_at__gte=last_7_days
    ).select_related('user', 'content_type').order_by('-created_at')[:15]

    for log in recent_logs:
        activity_type = 'system_activity'
        icon = 'fa-cog'
        color = 'text-blue-600'

        if log.content_type and log.content_type.model == 'mention':
            activity_type = 'mention_activity'
            icon = 'fa-microphone'
            color = 'text-purple-600'
        elif log.content_type and log.content_type.model == 'show':
            activity_type = 'show_activity'
            icon = 'fa-broadcast-tower'
            color = 'text-orange-600'
        elif log.content_type and log.content_type.model == 'client':
            activity_type = 'client_activity'
            icon = 'fa-building'
            color = 'text-indigo-600'

        recent_activity_feed.append({
            'type': activity_type,
            'timestamp': log.created_at,
            'user': log.user.get_full_name() if log.user else 'System',
            'description': log.description,
            'object': log.content_object,
            'icon': icon,
            'color': color
        })

    # Sort combined activity feed by timestamp
    recent_activity_feed.sort(key=lambda x: x['timestamp'], reverse=True)
    recent_activity_feed = recent_activity_feed[:20]  # Limit to 20 most recent

    # Get recent activity (last 5 completed readings) - keep for backward compatibility
    recent_activity = MentionReading.objects.filter(
        mention__client__organization=current_org,
        mention__status='scheduled',
        actual_read_time__isnull=False
    ).select_related('mention', 'show', 'presenter', 'mention__client').order_by('-actual_read_time')[:5]

    # Get upcoming schedule (next 3 days)
    upcoming_schedule = MentionReading.objects.filter(
        mention__client__organization=current_org,
        mention__status='scheduled',
        scheduled_date__gt=today,
        scheduled_date__lte=today + timedelta(days=3),
        actual_read_time__isnull=True
    ).select_related('mention', 'show', 'presenter', 'mention__client').order_by('scheduled_date', 'scheduled_time')[:10]

    # Enhanced chart data
    shows = Show.objects.filter(organization=current_org, is_active=True)
    show_labels = [show.name for show in shows]
    show_data = [
        MentionReading.objects.filter(
            mention__client__organization=current_org,
            mention__status='scheduled',
            show=show,
            scheduled_date__gte=today - timedelta(days=7)
        ).count() for show in shows
    ]

    # Status distribution
    status_labels = ['Completed', 'Upcoming', 'Pending Approval', 'Cancelled']
    status_data = [
        MentionReading.objects.filter(
            mention__client__organization=current_org,
            mention__status='scheduled',
            actual_read_time__isnull=False
        ).count(),
        MentionReading.objects.filter(
            mention__client__organization=current_org,
            mention__status='scheduled',
            actual_read_time__isnull=True,
            scheduled_date__gte=today
        ).count(),
        Mention.objects.filter(client__organization=current_org, status='pending').count(),
        Mention.objects.filter(client__organization=current_org, status='cancelled').count(),
    ]

    # Weekly activity data (last 7 days)
    weekly_labels = []
    weekly_data = []
    for i in range(7):
        date = today - timedelta(days=6-i)
        weekly_labels.append(date.strftime('%a'))
        daily_count = MentionReading.objects.filter(
            mention__client__organization=current_org,
            mention__status='scheduled',
            scheduled_date=date,
            actual_read_time__isnull=False
        ).count()
        weekly_data.append(daily_count)

    # Top clients by mentions (last 30 days)
    top_clients = Mention.objects.filter(
        client__organization=current_org,
        created_at__date__gte=last_month
    ).values('client__name').annotate(
        count=models.Count('id')
    ).order_by('-count')[:5]

    client_labels = [client['client__name'] for client in top_clients]
    client_data = [client['count'] for client in top_clients]

    chart_data = {
        'show_labels': json.dumps(show_labels),
        'show_data': json.dumps(show_data),
        'status_labels': json.dumps(status_labels),
        'status_data': json.dumps(status_data),
        'weekly_labels': json.dumps(weekly_labels),
        'weekly_data': json.dumps(weekly_data),
        'client_labels': json.dumps(client_labels),
        'client_data': json.dumps(client_data),
    }

    # Get alerts and notifications
    alerts = []

    # Check for conflicts
    from apps.mentions.services import ConflictDetectionService
    conflicts = ConflictDetectionService.detect_conflicts(organization=current_org)
    if conflicts:
        alerts.append({
            'type': 'warning',
            'title': 'Scheduling Conflicts Detected',
            'message': f'{len(conflicts)} scheduling conflicts need attention.',
            'action_url': reverse('mentions:conflict_detection'),
            'action_text': 'View Conflicts'
        })

    # Check for overdue approvals
    overdue_mentions = Mention.objects.filter(
        client__organization=current_org,
        status='pending',
        created_at__lt=timezone.now() - timedelta(hours=24)
    ).count()

    if overdue_mentions > 0:
        alerts.append({
            'type': 'error',
            'title': 'Overdue Approvals',
            'message': f'{overdue_mentions} mentions pending approval for over 24 hours.',
            'action_url': reverse('mentions:approval_workflow'),
            'action_text': 'Review Now'
        })

    context = {
        'stats': stats,
        'todays_schedule': todays_schedule,
        'pending_mentions': pending_mentions,
        'recent_activity': recent_activity,
        'upcoming_schedule': upcoming_schedule,
        'chart_data': chart_data,
        'alerts': alerts,
        'current_organization': current_org,

        # New enhanced data
        'industry_analytics': industry_analytics,
        'show_updates': show_updates,
        'recurring_stats': recurring_stats,
        'mention_activity': mention_activity,
        'recent_activity_feed': recent_activity_feed,
    }

    return render(request, 'core/dashboard.html', context)


def calculate_growth(current, previous):
    """Calculate growth percentage"""
    if previous == 0:
        return 100 if current > 0 else 0
    return round(((current - previous) / previous) * 100, 1)


class ClientListView(LoginRequiredMixin, OrganizationDataFilterMixin, ListView):
    model = Client
    template_name = 'core/client_list.html'
    context_object_name = 'clients'
    paginate_by = 20

    def get_queryset(self):
        queryset = super().get_queryset()
        search = self.request.GET.get('search')
        status = self.request.GET.get('status')
        industry = self.request.GET.get('industry')

        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) |
                Q(contact_person__icontains=search) |
                Q(email__icontains=search)
            )

        if status == 'active':
            queryset = queryset.filter(is_active=True)
        elif status == 'inactive':
            queryset = queryset.filter(is_active=False)

        if industry:
            queryset = queryset.filter(industry=industry)

        return queryset.order_by('name')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Get current organization
        current_org = get_current_organization(self.request)
        if current_org:
            # Add industry choices for the filter dropdown (excluding empty choice)
            choices = Client.get_industry_choices(current_org)
            context['industry_choices'] = [(code, name) for code, name in choices if code]
        return context


class ClientDetailView(LoginRequiredMixin, OrganizationDataFilterMixin, DetailView):
    model = Client
    template_name = 'core/client_detail.html'
    context_object_name = 'client'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        client = self.get_object()

        # Get mentions with related data
        mentions = client.mention_set.select_related('created_by', 'approved_by').order_by('-created_at')

        # Calculate statistics
        from datetime import datetime, timedelta
        from django.utils import timezone
        from django.db.models import Count, Q, Avg, Sum

        now = timezone.now()
        thirty_days_ago = now - timedelta(days=30)

        # Mention statistics
        context['mention_stats'] = {
            'total': mentions.count(),
            'pending': mentions.filter(status='pending').count(),
            'scheduled': mentions.filter(status='scheduled').count(),
            'completed': mentions.filter(status='read').count(),
            'cancelled': mentions.filter(status='cancelled').count(),
            'this_month': mentions.filter(created_at__gte=thirty_days_ago).count(),
            'avg_duration': mentions.aggregate(avg_duration=Avg('duration_seconds'))['avg_duration'] or 0,
            'total_airtime': mentions.filter(status='read').aggregate(total=Sum('duration_seconds'))['total'] or 0,
        }

        # Recent mentions (last 10)
        context['recent_mentions'] = mentions[:10]

        # Monthly activity for the last 6 months
        monthly_data = []
        for i in range(6):
            month_start = (now - timedelta(days=30*i)).replace(day=1)
            month_end = (month_start + timedelta(days=32)).replace(day=1) - timedelta(days=1)
            count = mentions.filter(created_at__range=[month_start, month_end]).count()
            monthly_data.append({
                'month': month_start.strftime('%b %Y'),
                'count': count
            })
        context['monthly_activity'] = list(reversed(monthly_data))

        # Status breakdown
        context['status_breakdown'] = mentions.values('status').annotate(count=Count('status'))

        # Priority breakdown
        context['priority_breakdown'] = mentions.values('priority').annotate(count=Count('priority'))

        return context


class ClientCreateView(LoginRequiredMixin, OrganizationDataFilterMixin, CreateView):
    model = Client
    form_class = ClientForm
    template_name = 'core/client_form.html'
    success_url = reverse_lazy('core:client_list')

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['organization'] = get_current_organization(self.request)
        return kwargs

    def form_valid(self, form):
        # Set the organization for the new client
        current_org = get_current_organization(self.request)
        if current_org:
            form.instance.organization = current_org
        messages.success(self.request, 'Client created successfully!')
        return super().form_valid(form)


class ClientUpdateView(LoginRequiredMixin, OrganizationDataFilterMixin, UpdateView):
    model = Client
    form_class = ClientForm
    template_name = 'core/client_form.html'
    success_url = reverse_lazy('core:client_list')

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['organization'] = get_current_organization(self.request)
        return kwargs

    def form_valid(self, form):
        messages.success(self.request, 'Client updated successfully!')
        return super().form_valid(form)


class ClientDeleteView(LoginRequiredMixin, OrganizationDataFilterMixin, DeleteView):
    model = Client
    template_name = 'core/client_confirm_delete.html'
    success_url = reverse_lazy('core:client_list')

    def delete(self, request, *args, **kwargs):
        messages.success(request, 'Client deleted successfully!')
        return super().delete(request, *args, **kwargs)


# Industry Management Views
@method_decorator(require_permission('manage_settings'), name='dispatch')
class IndustryListView(LoginRequiredMixin, OrganizationDataFilterMixin, ListView):
    model = Industry
    template_name = 'core/industry_list.html'
    context_object_name = 'industries'
    paginate_by = 20

    def get_queryset(self):
        queryset = super().get_queryset()
        search = self.request.GET.get('search')
        status = self.request.GET.get('status')

        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) |
                Q(code__icontains=search) |
                Q(description__icontains=search)
            )

        if status == 'active':
            queryset = queryset.filter(is_active=True)
        elif status == 'inactive':
            queryset = queryset.filter(is_active=False)

        return queryset.order_by('name')


@method_decorator(require_permission('manage_settings'), name='dispatch')
class IndustryDetailView(LoginRequiredMixin, OrganizationDataFilterMixin, DetailView):
    model = Industry
    template_name = 'core/industry_detail.html'
    context_object_name = 'industry'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        industry = self.get_object()

        # Get clients using this industry (by industry code)
        clients = Client.objects.filter(
            organization=industry.organization,
            industry=industry.code,
            is_active=True
        ).order_by('name')

        context['clients'] = clients
        context['client_count'] = Client.objects.filter(
            organization=industry.organization,
            industry=industry.code
        ).count()

        return context


@method_decorator(require_permission('manage_settings'), name='dispatch')
class IndustryCreateView(LoginRequiredMixin, OrganizationDataFilterMixin, CreateView):
    model = Industry
    form_class = IndustryForm
    template_name = 'core/industry_form.html'
    success_url = reverse_lazy('core:industry_list')

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['organization'] = get_current_organization(self.request)
        return kwargs

    def form_valid(self, form):
        current_org = get_current_organization(self.request)
        if current_org:
            form.instance.organization = current_org
        messages.success(self.request, 'Industry created successfully!')
        return super().form_valid(form)


@method_decorator(require_permission('manage_settings'), name='dispatch')
class IndustryUpdateView(LoginRequiredMixin, OrganizationDataFilterMixin, UpdateView):
    model = Industry
    form_class = IndustryForm
    template_name = 'core/industry_form.html'
    success_url = reverse_lazy('core:industry_list')

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['organization'] = get_current_organization(self.request)
        return kwargs

    def form_valid(self, form):
        messages.success(self.request, 'Industry updated successfully!')
        return super().form_valid(form)


@method_decorator(require_permission('manage_settings'), name='dispatch')
class IndustryDeleteView(LoginRequiredMixin, OrganizationDataFilterMixin, DeleteView):
    model = Industry
    template_name = 'core/industry_confirm_delete.html'
    success_url = reverse_lazy('core:industry_list')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        industry = self.get_object()

        # Get clients using this industry
        clients = Client.objects.filter(
            organization=industry.organization,
            industry=industry.code
        )

        context['clients'] = clients
        context['client_count'] = clients.count()
        return context

    def delete(self, request, *args, **kwargs):
        industry = self.get_object()

        # Check if any clients are using this industry
        client_count = Client.objects.filter(
            organization=industry.organization,
            industry=industry.code
        ).count()

        if client_count > 0:
            messages.error(request, f'Cannot delete industry "{industry.name}" because it is used by {client_count} client(s). Please reassign those clients to other industries first.')
            return redirect('core:industry_detail', pk=industry.pk)

        messages.success(request, f'Industry "{industry.name}" deleted successfully!')
        return super().delete(request, *args, **kwargs)


# Presenter Views
@method_decorator(require_permission('manage_presenters'), name='dispatch')
class PresenterListView(LoginRequiredMixin, OrganizationDataFilterMixin, ListView):
    model = Presenter
    template_name = 'core/presenter_list.html'
    context_object_name = 'presenters'
    paginate_by = 20

    def get_queryset(self):
        queryset = super().get_queryset()
        search = self.request.GET.get('search')
        status = self.request.GET.get('status')

        if search:
            queryset = queryset.filter(
                Q(first_name__icontains=search) |
                Q(last_name__icontains=search) |
                Q(stage_name__icontains=search) |
                Q(email__icontains=search)
            )

        if status == 'active':
            queryset = queryset.filter(is_active=True)
        elif status == 'inactive':
            queryset = queryset.filter(is_active=False)

        return queryset.order_by('first_name', 'last_name')


@method_decorator(require_permission('manage_presenters'), name='dispatch')
class PresenterDetailView(LoginRequiredMixin, OrganizationDataFilterMixin, DetailView):
    model = Presenter
    template_name = 'core/presenter_detail.html'
    context_object_name = 'presenter'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        presenter = self.object

        # Get presenter's availability
        context['availability'] = PresenterAvailability.objects.filter(
            presenter=presenter
        ).order_by('day_of_week', 'start_time')

        # Get presenter's skills
        context['skills'] = PresenterSkill.objects.filter(
            presenter=presenter
        ).order_by('category', 'name')

        # Get presenter's notes (filter private notes for non-staff)
        notes = PresenterNote.objects.filter(presenter=presenter).select_related('author')
        if not self.request.user.is_staff:
            notes = notes.filter(is_private=False)
        context['notes'] = notes.order_by('-created_at')[:10]

        # Get presenter's shows through ShowPresenter relationship
        from apps.shows.models import ShowPresenter
        context['shows'] = ShowPresenter.objects.filter(
            presenter=presenter,
            is_active=True
        ).select_related('show').order_by('-is_primary', 'show__name')[:10]

        # Get recent mentions through MentionReading
        from apps.mentions.models import MentionReading
        context['recent_mentions'] = MentionReading.objects.filter(
            presenter=presenter,
            mention__status='scheduled'
        ).select_related('mention', 'mention__client', 'show').order_by('-created_at')[:10]

        return context





@method_decorator(require_permission('manage_presenters'), name='dispatch')
class PresenterUpdateView(LoginRequiredMixin, OrganizationDataFilterMixin, UpdateView):
    model = Presenter
    template_name = 'core/presenter_form.html'
    fields = ['first_name', 'last_name', 'stage_name', 'email', 'phone', 'bio', 'profile_picture', 'experience_years', 'is_active']
    success_url = reverse_lazy('core:presenter_list')

    def form_valid(self, form):
        messages.success(self.request, 'Presenter updated successfully!')
        return super().form_valid(form)


@method_decorator(require_permission('manage_presenters'), name='dispatch')
class PresenterDeleteView(LoginRequiredMixin, OrganizationDataFilterMixin, DeleteView):
    model = Presenter
    template_name = 'core/presenter_confirm_delete.html'
    success_url = reverse_lazy('core:presenter_list')

    def delete(self, request, *args, **kwargs):
        messages.success(request, 'Presenter deleted successfully!')
        return super().delete(request, *args, **kwargs)


@login_required
def presenter_dashboard(request, pk):
    """Dashboard view for individual presenter"""
    from apps.mentions.models import MentionReading
    from apps.shows.models import ShowPresenter
    from django.db.models import Count, Avg, Q
    from datetime import datetime, timedelta
    from apps.organizations.middleware import get_current_membership, user_has_permission

    presenter = get_object_or_404(Presenter, pk=pk)
    today = datetime.now().date()
    last_week = today - timedelta(days=7)
    last_month = today - timedelta(days=30)

    # Get current organization
    current_org = get_current_organization(request)
    if not current_org:
        return redirect('organizations:list')

    # Permission check: Only allow access if user is the presenter themselves OR has manage_presenters permission
    current_membership = get_current_membership(request)
    if not current_membership:
        messages.error(request, 'No organization membership found.')
        return redirect('organizations:list')

    # Check if user is the presenter themselves
    is_own_dashboard = False
    try:
        user_presenter = Presenter.objects.get(user=request.user, organization=current_org, is_active=True)
        is_own_dashboard = (user_presenter.pk == presenter.pk)
    except Presenter.DoesNotExist:
        pass

    # If not their own dashboard, check if they have admin permissions
    if not is_own_dashboard and not user_has_permission(request, 'manage_presenters'):
        messages.error(request, 'Access denied. You can only view your own presenter dashboard.')
        return redirect('core:dashboard')

    # Ensure presenter belongs to current organization
    if presenter.organization != current_org:
        messages.error(request, 'Presenter not found.')
        return redirect('core:presenter_list')

    # Check if current user can access this presenter dashboard
    current_membership = get_current_membership(request)
    can_access = False

    if current_membership:
        # User can access if they are the presenter themselves
        if presenter.user == request.user:
            can_access = True
        # Or if they have management permissions
        elif current_membership.has_permission('manage_presenters'):
            can_access = True
        # Or if they have view permissions and this is their assigned presenter
        elif current_membership.has_permission('view_presenter_dashboard'):
            can_access = True

    if not can_access:
        messages.error(request, 'Access denied. You can only view your own presenter dashboard.')
        return redirect('core:dashboard')

    # Get shows where this presenter is assigned
    presenter_show_ids = ShowPresenter.objects.filter(
        presenter=presenter,
        is_active=True
    ).values_list('show_id', flat=True)

    # Today's schedule - get mentions for shows where presenter is assigned
    todays_schedule = MentionReading.objects.filter(
        show_id__in=presenter_show_ids,
        mention__status='scheduled',
        scheduled_date=today
    ).select_related('mention', 'mention__client', 'show').order_by('scheduled_time')

    # Upcoming schedule (next 7 days) - get mentions for shows where presenter is assigned
    upcoming_schedule = MentionReading.objects.filter(
        show_id__in=presenter_show_ids,
        mention__status='scheduled',
        scheduled_date__gt=today,
        scheduled_date__lte=today + timedelta(days=7)
    ).select_related('mention', 'mention__client', 'show').order_by('scheduled_date', 'scheduled_time')[:10]

    # Recent completed readings - only show readings this presenter actually completed
    recent_completed = MentionReading.objects.filter(
        presenter=presenter,
        mention__status='scheduled',
        actual_read_time__isnull=False
    ).select_related('mention', 'mention__client', 'show').order_by('-actual_read_time')[:10]

    # Enhanced statistics for the improved dashboard
    completed_today = todays_schedule.filter(actual_read_time__isnull=False).count()
    remaining_today = todays_schedule.filter(actual_read_time__isnull=True).count()

    # Week statistics
    week_start = today - timedelta(days=today.weekday())
    week_end = week_start + timedelta(days=6)
    week_total = MentionReading.objects.filter(
        show_id__in=presenter_show_ids,
        mention__status='scheduled',
        scheduled_date__range=[week_start, week_end]
    ).count()

    stats = {
        'total_readings': MentionReading.objects.filter(show_id__in=presenter_show_ids, mention__status='scheduled').count(),  # All readings for presenter's shows
        'completed_readings': MentionReading.objects.filter(
            presenter=presenter,
            mention__status='scheduled',
            actual_read_time__isnull=False
        ).count(),  # Only readings this presenter actually completed
        'scheduled_today': todays_schedule.count(),
        'completed_today': completed_today,
        'remaining_today': remaining_today,
        'week_total': week_total,
        'upcoming_week': MentionReading.objects.filter(
            show_id__in=presenter_show_ids,
            mention__status='scheduled',
            scheduled_date__gt=today,
            scheduled_date__lte=today + timedelta(days=7)
        ).count(),
        'completion_rate': 0,
        'avg_duration': 0,
        'shows_count': ShowPresenter.objects.filter(presenter=presenter, is_active=True).count(),
    }

    # Calculate completion rate based on presenter's actual completions vs available readings
    total_available = MentionReading.objects.filter(show_id__in=presenter_show_ids, mention__status='scheduled').count()
    if total_available > 0:
        stats['completion_rate'] = round((stats['completed_readings'] / total_available) * 100, 1)

    # Calculate average duration for readings this presenter completed
    avg_duration = MentionReading.objects.filter(
        presenter=presenter,
        mention__status='scheduled',
        actual_read_time__isnull=False,
        duration_seconds__isnull=False
    ).aggregate(avg=Avg('duration_seconds'))['avg']
    if avg_duration:
        stats['avg_duration'] = round(avg_duration, 0)

    # Performance over time (last 30 days) - only count readings this presenter actually completed
    performance_data = []
    for i in range(30):
        date = today - timedelta(days=29-i)
        completed = MentionReading.objects.filter(
            presenter=presenter,
            mention__status='scheduled',
            scheduled_date=date,
            actual_read_time__isnull=False
        ).count()
        performance_data.append({
            'date': date.strftime('%Y-%m-%d'),
            'completed': completed
        })

    # Shows the presenter is associated with
    presenter_shows = ShowPresenter.objects.filter(
        presenter=presenter,
        is_active=True
    ).select_related('show').order_by('-is_primary', 'show__name')

    context = {
        'presenter': presenter,
        'stats': stats,
        'todays_schedule': todays_schedule,
        'upcoming_schedule': upcoming_schedule,
        'recent_completed': recent_completed,
        'presenter_shows': presenter_shows,
        'performance_data': performance_data,
    }

    return render(request, 'core/presenter_dashboard.html', context)


@login_required
def presenter_calendar(request, pk):
    """Calendar view for individual presenter showing only their show mentions"""
    from apps.mentions.models import MentionReading
    from apps.shows.models import ShowPresenter
    from calendar import monthrange
    from datetime import datetime, timedelta
    from apps.organizations.middleware import get_current_membership, user_has_permission

    presenter = get_object_or_404(Presenter, pk=pk)

    # Get current organization
    current_org = get_current_organization(request)
    if not current_org:
        return redirect('organizations:list')

    # Check permissions - only allow access to own dashboard or admin access
    current_membership = get_current_membership(request)
    if not current_membership:
        messages.error(request, 'No organization membership found.')
        return redirect('organizations:list')

    # Allow access if user is the presenter or has admin permissions
    can_access = False
    if current_membership.role == 'admin':
        can_access = True
    elif presenter.user == request.user:
        can_access = True
    elif user_has_permission(request, 'manage_presenters'):
        can_access = True

    if not can_access:
        messages.error(request, 'You do not have permission to view this calendar.')
        return redirect('core:dashboard')

    # Get date parameters
    date_str = request.GET.get('date')
    view = request.GET.get('view', 'month')

    if date_str:
        current_date = datetime.strptime(date_str, '%Y-%m-%d').date()
    else:
        current_date = datetime.now().date()

    today = datetime.now().date()

    # Get shows where this presenter is assigned
    presenter_shows = ShowPresenter.objects.filter(
        presenter=presenter,
        is_active=True
    ).values_list('show_id', flat=True)

    if view == 'day':
        # Day view - get all mentions for shows where this presenter is assigned
        schedule = MentionReading.objects.filter(
            show_id__in=presenter_shows,
            mention__status='scheduled',
            scheduled_date=current_date
        ).select_related('mention', 'show', 'mention__client').order_by('scheduled_time')

        prev_date = current_date - timedelta(days=1)
        next_date = current_date + timedelta(days=1)

        context = {
            'presenter': presenter,
            'current_date': current_date,
            'prev_date': prev_date,
            'next_date': next_date,
            'today': today,
            'view': view,
            'schedule': schedule,
            'time_slots': list(range(0, 24)),  # 24 hours to show all mentions
        }

    elif view == 'week':
        # Week view
        start_of_week = current_date - timedelta(days=current_date.weekday())
        end_of_week = start_of_week + timedelta(days=6)

        # Generate week days
        week_days = []
        for i in range(7):
            day_date = start_of_week + timedelta(days=i)
            day_schedule = MentionReading.objects.filter(
                show_id__in=presenter_shows,
                mention__status='scheduled',
                scheduled_date=day_date
            ).select_related('mention', 'show', 'mention__client').order_by('scheduled_time')

            week_days.append({
                'date': day_date,
                'schedule': day_schedule,
                'is_today': day_date == today
            })

        prev_date = start_of_week - timedelta(days=7)
        next_date = start_of_week + timedelta(days=7)

        context = {
            'presenter': presenter,
            'current_date': current_date,
            'prev_date': prev_date,
            'next_date': next_date,
            'today': today,
            'view': view,
            'week_days': week_days,
            'time_slots': list(range(0, 24)),  # 24 hours to show all mentions
        }

    else:  # month view
        year = current_date.year
        month = current_date.month

        # Calculate previous and next month
        if month == 1:
            prev_month = 12
            prev_year = year - 1
        else:
            prev_month = month - 1
            prev_year = year

        if month == 12:
            next_month = 1
            next_year = year + 1
        else:
            next_month = month + 1
            next_year = year

        prev_date = datetime(prev_year, prev_month, 1).date()
        next_date = datetime(next_year, next_month, 1).date()

        # Get first day of month and calculate calendar start
        first_day = datetime(year, month, 1).date()
        calendar_start = first_day - timedelta(days=first_day.weekday())

        # Generate calendar weeks
        month_weeks = []
        for week in range(6):
            week_data = []
            for day in range(7):
                calendar_date = calendar_start + timedelta(days=week * 7 + day)

                # Get schedule for this day (filtered by presenter's shows)
                day_schedule = MentionReading.objects.filter(
                    show_id__in=presenter_shows,
                    mention__status='scheduled',
                    scheduled_date=calendar_date
                ).select_related('mention', 'show', 'mention__client')[:3]  # Limit to 3 for display

                is_other_month = calendar_date.month != month
                is_today = calendar_date == today

                week_data.append({
                    'date': calendar_date,
                    'day': calendar_date.day,
                    'schedule': day_schedule,
                    'is_other_month': is_other_month,
                    'is_today': is_today
                })
            month_weeks.append(week_data)

        context = {
            'presenter': presenter,
            'current_date': current_date,
            'prev_date': prev_date,
            'next_date': next_date,
            'today': today,
            'view': view,
            'month_weeks': month_weeks,
            'day_names': ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
        }

    return render(request, 'core/presenter_calendar.html', context)


@presenter_only
def my_presenter_dashboard(request):
    """Dashboard for authenticated presenter - redirects to their specific dashboard"""
    from apps.organizations.middleware import get_current_organization, get_current_membership

    # Get current organization and membership
    current_org = get_current_organization(request)
    if not current_org:
        messages.error(request, 'Please select an organization.')
        return redirect('organizations:list')

    current_membership = get_current_membership(request)
    if not current_membership:
        messages.error(request, 'No organization membership found.')
        return redirect('organizations:list')

    # Check if user has presenter role
    if current_membership.role != 'presenter':
        messages.error(request, 'Access denied. You need presenter role to access this dashboard.')
        return redirect('core:dashboard')

    # Try to find or create presenter profile for this user
    try:
        presenter = Presenter.objects.get(
            user=request.user,
            organization=current_org,
            is_active=True
        )
    except Presenter.DoesNotExist:
        # Create presenter profile if it doesn't exist
        presenter = Presenter.objects.create(
            user=request.user,
            organization=current_org,
            first_name=request.user.first_name or request.user.username,
            last_name=request.user.last_name or '',
            email=request.user.email,
            is_active=True
        )
        messages.success(request, 'Presenter profile created successfully!')

    # Redirect to the specific presenter dashboard
    return redirect('core:presenter_dashboard', pk=presenter.pk)


@presenter_only
def live_show_page(request):
    """Live show interface for presenters"""
    from apps.mentions.models import MentionReading
    from apps.shows.models import ShowPresenter
    from django.db.models import Q
    from datetime import datetime, timedelta
    from apps.organizations.middleware import get_current_organization, get_current_membership

    # Get current organization and membership
    current_org = get_current_organization(request)
    if not current_org:
        messages.error(request, 'Please select an organization.')
        return redirect('organizations:list')

    current_membership = get_current_membership(request)
    if not current_membership:
        messages.error(request, 'No organization membership found.')
        return redirect('organizations:list')

    # Get presenter profile
    try:
        presenter = Presenter.objects.get(
            user=request.user,
            organization=current_org,
            is_active=True
        )
    except Presenter.DoesNotExist:
        messages.error(request, 'Presenter profile not found.')
        return redirect('core:dashboard')

    today = datetime.now().date()
    current_time = datetime.now().time()

    # Get today's mentions for shows where this presenter is assigned
    # Since presenters are now only assigned when they mark mentions as read,
    # we need to get mentions for shows where this presenter is a show presenter
    presenter_shows = presenter.showpresenter_set.filter(is_active=True).values_list('show', flat=True)

    todays_mentions = MentionReading.objects.filter(
        show__in=presenter_shows,
        mention__status='scheduled',
        scheduled_date=today
    ).select_related('mention', 'mention__client', 'show').order_by('scheduled_time')

    # Get current/next mention
    next_mention = todays_mentions.filter(
        scheduled_time__gte=current_time,
        actual_read_time__isnull=True
    ).first()

    # Get presenter's shows for today
    presenter_shows = ShowPresenter.objects.filter(
        presenter=presenter,
        is_active=True
    ).select_related('show')

    # Check if presenter is currently on air
    # First check for active show sessions (shows that haven't been ended early)
    from apps.shows.models import ShowSession

    current_show = None
    show_session = None

    # Check for active show session first
    active_session = ShowSession.objects.filter(
        presenter=presenter,
        date=today,
        actual_end_time__isnull=True
    ).first()

    if active_session:
        current_show = active_session.show
        show_session = active_session
    else:
        # If no active session, check scheduled shows
        for show_presenter in presenter_shows:
            show = show_presenter.show
            if show.start_time <= current_time <= show.end_time:
                # Check if this show was ended early today
                ended_session = ShowSession.objects.filter(
                    show=show,
                    presenter=presenter,
                    date=today,
                    ended_early=True
                ).first()

                if not ended_session:
                    current_show = show
                    break

    # Calculate mentions statistics
    mentions_completed = todays_mentions.filter(actual_read_time__isnull=False).count()
    mentions_remaining = todays_mentions.count() - mentions_completed

    # Calculate show duration if current show exists
    show_duration_hours = None
    if current_show:
        from datetime import datetime, timedelta
        start_dt = datetime.combine(today, current_show.start_time)
        end_dt = datetime.combine(today, current_show.end_time)
        if end_dt < start_dt:  # Show crosses midnight
            end_dt += timedelta(days=1)
        duration = end_dt - start_dt
        show_duration_hours = duration.total_seconds() / 3600

    # Get weather data for the organization
    weather_data = get_organization_weather(current_org)

    # Check if current show was ended early today
    show_ended_early = False
    if current_show:
        ended_session = ShowSession.objects.filter(
            show=current_show,
            presenter=presenter,
            date=today,
            ended_early=True
        ).first()
        show_ended_early = ended_session is not None

    # Get alignment settings
    from apps.settings.models import OrganizationSettings
    org_settings, created = OrganizationSettings.objects.get_or_create(
        organization=current_org
    )

    # Get industry colors for dynamic styling
    industry_colors = {}
    industries = Industry.objects.filter(organization=current_org, is_active=True)
    for industry in industries:
        industry_colors[industry.code] = industry.color

    context = {
        'presenter': presenter,
        'todays_mentions': todays_mentions,
        'next_mention': next_mention,
        'current_show': current_show,
        'presenter_shows': presenter_shows,
        'is_live': current_show is not None and not show_ended_early,
        'show_ended_early': show_ended_early,
        'mentions_completed': mentions_completed,
        'mentions_remaining': mentions_remaining,
        'show_duration_hours': show_duration_hours,
        'weather_data': weather_data,
        'alignment_mode': org_settings.mention_alignment_mode,
        'industry_alignments': org_settings.industry_alignment_settings,
        'industry_colors': industry_colors,
    }

    return render(request, 'core/live_show.html', context)


@presenter_only
def mark_mention_read(request, reading_id):
    """Mark a mention reading as completed"""
    if request.method == 'POST':
        try:
            reading = get_object_or_404(MentionReading, id=reading_id)

            # Get the current presenter
            current_org = get_current_organization(request)
            presenter = Presenter.objects.get(
                user=request.user,
                organization=current_org,
                is_active=True
            )

            # Assign the presenter and mark as read
            reading.presenter = presenter  # Assign presenter when marking as read
            reading.read_by = presenter.display_name  # Update read_by field with presenter name
            reading.actual_read_time = timezone.now()
            reading.save()

            return JsonResponse({
                'success': True,
                'message': 'Mention marked as read',
                'reading_id': reading_id
            })

        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)})

    return JsonResponse({'success': False, 'error': 'Invalid request method'})


@presenter_only
def skip_mention(request, reading_id):
    """Skip a mention reading"""
    if request.method == 'POST':
        try:
            reading = get_object_or_404(MentionReading, id=reading_id)

            # Get the current presenter
            current_org = get_current_organization(request)
            presenter = Presenter.objects.get(
                user=request.user,
                organization=current_org,
                is_active=True
            )

            # Verify the presenter can access this show
            if not presenter.showpresenter_set.filter(show=reading.show, is_active=True).exists():
                return JsonResponse({'success': False, 'error': 'Access denied'})

            # Assign presenter and mark as skipped
            reading.presenter = presenter  # Assign presenter when skipping
            reading.read_by = presenter.display_name  # Update read_by field with presenter name
            reading.notes = (reading.notes or '') + f'\nSkipped at {timezone.now()}'
            reading.save()

            return JsonResponse({
                'success': True,
                'message': 'Mention skipped',
                'reading_id': reading_id
            })

        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)})

    return JsonResponse({'success': False, 'error': 'Invalid request method'})


@presenter_only
def end_show(request):
    """End the current show early"""
    if request.method == 'POST':
        try:
            from apps.shows.models import ShowPresenter
            from datetime import datetime

            # Get the current presenter
            current_org = get_current_organization(request)
            presenter = Presenter.objects.get(
                user=request.user,
                organization=current_org,
                is_active=True
            )

            # Get current time
            current_time = datetime.now().time()

            # Find the current show
            presenter_shows = ShowPresenter.objects.filter(
                presenter=presenter,
                is_active=True
            ).select_related('show')

            current_show = None
            for show_presenter in presenter_shows:
                show = show_presenter.show
                if show.start_time <= current_time <= show.end_time:
                    current_show = show
                    break

            if not current_show:
                return JsonResponse({
                    'success': False,
                    'error': 'No active show found'
                })

            # Create or update show session to track early end
            from apps.shows.models import ShowSession
            from django.utils import timezone

            today = timezone.now().date()
            session, created = ShowSession.objects.get_or_create(
                show=current_show,
                presenter=presenter,
                date=today,
                defaults={
                    'actual_start_time': timezone.now().replace(
                        hour=current_show.start_time.hour,
                        minute=current_show.start_time.minute,
                        second=0,
                        microsecond=0
                    )
                }
            )

            # End the session early
            session.actual_end_time = timezone.now()
            session.ended_early = True
            session.end_reason = 'early'
            session.notes = f'Show ended early by {presenter.display_name}'
            session.save()

            return JsonResponse({
                'success': True,
                'message': f'Show "{current_show.name}" ended successfully',
                'show_name': current_show.name,
                'ended_early': True,
                'end_time': session.actual_end_time.strftime('%H:%M:%S')
            })

        except Presenter.DoesNotExist:
            return JsonResponse({'success': False, 'error': 'Presenter not found'})
        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)})

    return JsonResponse({'success': False, 'error': 'Invalid request method'})


def presenter_login_page(request):
    """Presenter-specific login page"""
    # If user is already logged in, redirect appropriately
    if request.user.is_authenticated:
        return redirect('core:presenter_login_redirect')

    return render(request, 'core/presenter_login.html')


@login_required
def presenter_login_redirect(request):
    """Special login redirect for presenters"""
    from apps.organizations.middleware import get_current_membership

    current_membership = get_current_membership(request)
    if current_membership and current_membership.role == 'presenter':
        return redirect('core:my_presenter_dashboard')
    else:
        return redirect('core:dashboard')


@presenter_only
def reorder_mentions(request):
    """Reorder mentions during live show"""
    if request.method != 'POST':
        return JsonResponse({'success': False, 'error': 'Invalid request method'}, status=405)

    try:
        import json
        from apps.mentions.models import MentionReading
        from apps.organizations.middleware import get_current_organization

        # Get current organization and presenter
        current_org = get_current_organization(request)
        if not current_org:
            return JsonResponse({'success': False, 'error': 'No organization found'}, status=400)

        presenter = Presenter.objects.get(
            user=request.user,
            organization=current_org,
            is_active=True
        )

        # Parse the new order from request
        data = json.loads(request.body)
        mention_order = data.get('mention_order', [])

        if not mention_order:
            return JsonResponse({'success': False, 'error': 'No mention order provided'}, status=400)

        # Verify all mentions belong to today's readings for this presenter
        today = timezone.now().date()
        todays_readings = MentionReading.objects.filter(
            mention__client__organization=current_org,
            mention__status='scheduled',
            scheduled_date=today,
            actual_read_time__isnull=True  # Only unread mentions can be reordered
        ).select_related('mention', 'mention__client')

        # Create a mapping of reading IDs to readings
        readings_dict = {reading.id: reading for reading in todays_readings}

        # Validate that all provided IDs exist and belong to today
        for reading_id in mention_order:
            if reading_id not in readings_dict:
                return JsonResponse({
                    'success': False,
                    'error': f'Invalid mention reading ID: {reading_id}'
                }, status=400)

        # Update the order by setting custom_order field
        # We'll use the index in the list as the order value
        for index, reading_id in enumerate(mention_order):
            reading = readings_dict[reading_id]
            # Add a custom_order field to track manual reordering
            # For now, we'll update the scheduled_time to reflect the new order
            # This is a simple approach - in production you might want a dedicated order field
            base_time = reading.scheduled_time.replace(second=0, microsecond=0)
            new_time = base_time.replace(second=index)
            reading.scheduled_time = new_time
            reading.save()

        return JsonResponse({
            'success': True,
            'message': 'Mentions reordered successfully',
            'new_order': mention_order
        })

    except Presenter.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'Presenter not found'}, status=404)
    except json.JSONDecodeError:
        return JsonResponse({'success': False, 'error': 'Invalid JSON data'}, status=400)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)


@owner_or_admin_required
def live_show_monitor(request):
    """Live show monitoring dashboard for administrators"""
    from apps.mentions.models import MentionReading
    from apps.shows.models import Show, ShowPresenter, ShowSession
    from django.db.models import Count, Q, Avg, F, Case, When, IntegerField
    from datetime import datetime, timedelta

    # Get current organization
    current_org = get_current_organization(request)
    if not current_org:
        messages.error(request, 'Please select an organization.')
        return redirect('organizations:list')

    today = timezone.now().date()
    current_time = timezone.now().time()
    current_datetime = timezone.now()

    # Get all shows for today
    todays_shows = Show.objects.filter(
        organization=current_org,
        is_active=True,
        days_of_week__contains=[today.weekday()]
    ).select_related().prefetch_related('showpresenter_set__presenter')

    # Get active show sessions
    active_sessions = ShowSession.objects.filter(
        show__organization=current_org,
        date=today,
        actual_end_time__isnull=True
    ).select_related('show', 'presenter')

    # Get all mentions for today
    todays_mentions = MentionReading.objects.filter(
        mention__client__organization=current_org,
        mention__status='scheduled',
        scheduled_date=today
    ).select_related('mention', 'mention__client', 'show', 'presenter').order_by('scheduled_time')

    # Calculate show statistics
    show_stats = []
    for show in todays_shows:
        # Check if show is currently live
        is_live = show.start_time <= current_time <= show.end_time

        # Get show session if exists
        session = active_sessions.filter(show=show).first()

        # Get mentions for this show
        show_mentions = todays_mentions.filter(show=show)
        completed_mentions = show_mentions.filter(actual_read_time__isnull=False)
        pending_mentions = show_mentions.filter(actual_read_time__isnull=True)

        # Get active presenters
        active_presenters = show.showpresenter_set.filter(is_active=True)

        # Calculate progress
        total_mentions = show_mentions.count()
        completed_count = completed_mentions.count()
        progress_percentage = (completed_count / total_mentions * 100) if total_mentions > 0 else 0

        # Determine show status
        if session and session.ended_early:
            status = 'ended_early'
            status_display = 'Ended Early'
            status_color = 'red'
        elif is_live:
            status = 'live'
            status_display = 'ON AIR'
            status_color = 'green'
        elif current_time < show.start_time:
            status = 'upcoming'
            status_display = 'Upcoming'
            status_color = 'blue'
        else:
            status = 'completed'
            status_display = 'Completed'
            status_color = 'gray'

        show_stats.append({
            'show': show,
            'session': session,
            'is_live': is_live,
            'status': status,
            'status_display': status_display,
            'status_color': status_color,
            'active_presenters': active_presenters,
            'total_mentions': total_mentions,
            'completed_mentions': completed_count,
            'pending_mentions': pending_mentions.count(),
            'progress_percentage': round(progress_percentage, 1),
            'next_mention': pending_mentions.first(),
        })

    # Get overall statistics
    total_shows_today = todays_shows.count()
    live_shows = sum(1 for stat in show_stats if stat['is_live'])
    total_mentions_today = todays_mentions.count()
    completed_mentions_today = todays_mentions.filter(actual_read_time__isnull=False).count()
    pending_mentions_today = total_mentions_today - completed_mentions_today

    # Get active presenters
    active_presenters = Presenter.objects.filter(
        organization=current_org,
        is_active=True,
        showpresenter__show__in=todays_shows,
        showpresenter__is_active=True
    ).distinct().annotate(
        current_show_count=Count(
            'showpresenter__show',
            filter=Q(
                showpresenter__show__start_time__lte=current_time,
                showpresenter__show__end_time__gte=current_time,
                showpresenter__is_active=True
            )
        ),
        todays_mentions=Count(
            'mentionreading',
            filter=Q(mentionreading__scheduled_date=today)
        ),
        completed_mentions=Count(
            'mentionreading',
            filter=Q(
                mentionreading__scheduled_date=today,
                mentionreading__actual_read_time__isnull=False
            )
        )
    )

    # Get recent activity (last 2 hours)
    recent_activity_time = current_datetime - timedelta(hours=2)
    recent_activity = MentionReading.objects.filter(
        mention__client__organization=current_org,
        mention__status='scheduled',
        actual_read_time__gte=recent_activity_time
    ).select_related('mention', 'mention__client', 'presenter', 'show').order_by('-actual_read_time')[:20]

    # Get alerts and issues
    alerts = []

    # Check for overdue mentions (more than 15 minutes past scheduled time)
    overdue_time = current_datetime - timedelta(minutes=15)
    overdue_mentions = todays_mentions.filter(
        scheduled_time__lt=current_time,
        actual_read_time__isnull=True,
        scheduled_date=today
    ).count()

    if overdue_mentions > 0:
        alerts.append({
            'type': 'warning',
            'title': 'Overdue Mentions',
            'message': f'{overdue_mentions} mentions are overdue',
            'count': overdue_mentions
        })

    # Check for shows without active presenters
    shows_without_presenters = []
    for stat in show_stats:
        if stat['is_live'] and not stat['active_presenters'].exists():
            shows_without_presenters.append(stat['show'].name)

    if shows_without_presenters:
        alerts.append({
            'type': 'error',
            'title': 'Shows Without Presenters',
            'message': f'Shows without active presenters: {", ".join(shows_without_presenters)}',
            'count': len(shows_without_presenters)
        })

    # Check for shows ending soon (within 30 minutes)
    ending_soon = []
    for stat in show_stats:
        if stat['is_live']:
            show = stat['show']
            end_datetime = current_datetime.replace(
                hour=show.end_time.hour,
                minute=show.end_time.minute,
                second=0,
                microsecond=0
            )
            time_remaining = end_datetime - current_datetime
            if time_remaining.total_seconds() <= 1800:  # 30 minutes
                ending_soon.append({
                    'show': show,
                    'time_remaining': time_remaining
                })

    if ending_soon:
        alerts.append({
            'type': 'info',
            'title': 'Shows Ending Soon',
            'message': f'{len(ending_soon)} shows ending within 30 minutes',
            'count': len(ending_soon),
            'details': ending_soon
        })

    context = {
        'show_stats': show_stats,
        'active_presenters': active_presenters,
        'recent_activity': recent_activity,
        'alerts': alerts,
        'total_shows_today': total_shows_today,
        'live_shows': live_shows,
        'total_mentions_today': total_mentions_today,
        'completed_mentions_today': completed_mentions_today,
        'pending_mentions_today': pending_mentions_today,
        'current_organization': current_org,
        'current_time': current_time,
        'today': today,
    }

    return render(request, 'core/live_show_monitor.html', context)


@owner_or_admin_required
def live_show_monitor_api(request):
    """API endpoint for live show monitoring data"""
    from apps.mentions.models import MentionReading
    from apps.shows.models import Show, ShowSession
    from django.db.models import Count, Q
    from datetime import datetime, timedelta

    # Get current organization
    current_org = get_current_organization(request)
    if not current_org:
        return JsonResponse({'error': 'No organization found'}, status=400)

    today = timezone.now().date()
    current_time = timezone.now().time()
    current_datetime = timezone.now()

    # Get basic statistics
    todays_mentions = MentionReading.objects.filter(
        mention__client__organization=current_org,
        mention__status='scheduled',
        scheduled_date=today
    )

    total_mentions = todays_mentions.count()
    completed_mentions = todays_mentions.filter(actual_read_time__isnull=False).count()
    pending_mentions = total_mentions - completed_mentions

    # Get live shows count
    todays_shows = Show.objects.filter(
        organization=current_org,
        is_active=True,
        days_of_week__contains=[today.weekday()]
    )

    live_shows = 0
    for show in todays_shows:
        if show.start_time <= current_time <= show.end_time:
            live_shows += 1

    # Get recent activity (last 30 minutes)
    recent_activity_time = current_datetime - timedelta(minutes=30)
    recent_activity_count = todays_mentions.filter(
        actual_read_time__gte=recent_activity_time
    ).count()

    # Check for alerts
    alerts_count = 0

    # Overdue mentions
    overdue_mentions = todays_mentions.filter(
        scheduled_time__lt=current_time,
        actual_read_time__isnull=True,
        scheduled_date=today
    ).count()

    if overdue_mentions > 0:
        alerts_count += 1

    # Shows without presenters
    shows_without_presenters = 0
    for show in todays_shows:
        if show.start_time <= current_time <= show.end_time:
            if not show.showpresenter_set.filter(is_active=True).exists():
                shows_without_presenters += 1

    if shows_without_presenters > 0:
        alerts_count += 1

    return JsonResponse({
        'success': True,
        'data': {
            'total_shows_today': todays_shows.count(),
            'live_shows': live_shows,
            'total_mentions_today': total_mentions,
            'completed_mentions_today': completed_mentions,
            'pending_mentions_today': pending_mentions,
            'recent_activity_count': recent_activity_count,
            'alerts_count': alerts_count,
            'overdue_mentions': overdue_mentions,
            'shows_without_presenters': shows_without_presenters,
            'last_updated': current_datetime.isoformat(),
        }
    })


@owner_or_admin_required
def system_performance_api(request):
    """API endpoint for system performance metrics"""
    from apps.mentions.models import MentionReading
    from apps.shows.models import Show, ShowSession
    from apps.activity_logs.models import ActivityLog
    from datetime import datetime, timedelta

    # Get current organization
    current_org = get_current_organization(request)
    if not current_org:
        return JsonResponse({'error': 'No organization found'}, status=400)

    current_time = timezone.now()
    current_date = current_time.date()

    # Collect performance metrics
    metrics = {
        'timestamp': current_time.isoformat(),
        'mentions': {
            'pending_today': MentionReading.objects.filter(
                mention__client__organization=current_org,
                mention__status='scheduled',
                scheduled_date=current_date,
                actual_read_time__isnull=True
            ).count(),
            'completed_today': MentionReading.objects.filter(
                mention__client__organization=current_org,
                mention__status='scheduled',
                scheduled_date=current_date,
                actual_read_time__isnull=False
            ).count(),
            'overdue': MentionReading.objects.filter(
                mention__client__organization=current_org,
                mention__status='scheduled',
                scheduled_date__lt=current_date,
                actual_read_time__isnull=True
            ).count(),
        },
        'shows': {
            'active_sessions': ShowSession.objects.filter(
                show__organization=current_org,
                date=current_date,
                actual_end_time__isnull=True
            ).count(),
            'completed_today': ShowSession.objects.filter(
                show__organization=current_org,
                date=current_date,
                actual_end_time__isnull=False
            ).count(),
        },
        'activity': {
            'logs_last_hour': ActivityLog.objects.filter(
                organization=current_org,
                created_at__gte=current_time - timedelta(hours=1)
            ).count(),
            'errors_last_hour': ActivityLog.objects.filter(
                organization=current_org,
                created_at__gte=current_time - timedelta(hours=1),
                level__in=['error', 'critical']
            ).count(),
        }
    }

    # Calculate performance indicators
    total_mentions = metrics['mentions']['pending_today'] + metrics['mentions']['completed_today']
    completion_rate = 0
    if total_mentions > 0:
        completion_rate = (metrics['mentions']['completed_today'] / total_mentions) * 100

    # Calculate health score
    health_score = 100
    if metrics['activity']['errors_last_hour'] > 0:
        health_score -= min(metrics['activity']['errors_last_hour'] * 10, 50)
    if completion_rate < 80:
        health_score -= (80 - completion_rate)
    if metrics['activity']['logs_last_hour'] == 0:
        health_score -= 20

    metrics['performance'] = {
        'completion_rate': round(completion_rate, 2),
        'health_score': max(health_score, 0)
    }

    return JsonResponse({
        'success': True,
        'metrics': metrics
    })


@owner_or_admin_required
def system_performance_dashboard(request):
    """System performance monitoring dashboard for admins"""
    return render(request, 'core/system_performance.html')


# =============================================================================
# NOTIFICATION API ENDPOINTS
# =============================================================================

@login_required
def notifications_sync(request):
    """API endpoint for syncing notifications"""
    if request.method != 'GET':
        return JsonResponse({'error': 'GET method required'}, status=405)

    try:
        # For now, return empty notifications since we don't have a notification model yet
        # This prevents the 404 errors in the frontend
        return JsonResponse({
            'success': True,
            'notifications': []
        })
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


@login_required
def notifications_recent(request):
    """API endpoint for getting recent notifications"""
    if request.method != 'GET':
        return JsonResponse({'error': 'GET method required'}, status=405)

    try:
        # For now, return empty notifications since we don't have a notification model yet
        # This prevents the 404 errors in the frontend
        return JsonResponse({
            'success': True,
            'notifications': []
        })
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


@login_required
def notifications_unread_count(request):
    """API endpoint for getting unread notification count"""
    if request.method != 'GET':
        return JsonResponse({'error': 'GET method required'}, status=405)

    try:
        # For now, return 0 count since we don't have a notification model yet
        # This prevents the 404 errors in the frontend
        return JsonResponse({
            'success': True,
            'count': 0
        })
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


@login_required
def notifications_mark_read(request, notification_id):
    """API endpoint for marking a notification as read"""
    if request.method != 'POST':
        return JsonResponse({'error': 'POST method required'}, status=405)

    try:
        # For now, return success since we don't have a notification model yet
        # This prevents the 404 errors in the frontend
        return JsonResponse({
            'success': True,
            'message': 'Notification marked as read'
        })
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


@login_required
def notifications_mark_all_read(request):
    """API endpoint for marking all notifications as read"""
    if request.method != 'POST':
        return JsonResponse({'error': 'POST method required'}, status=405)

    try:
        # For now, return success since we don't have a notification model yet
        # This prevents the 404 errors in the frontend
        return JsonResponse({
            'success': True,
            'message': 'All notifications marked as read'
        })
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


@presenter_only
def get_weather_data(request):
    """AJAX endpoint to get weather data for a specific location"""
    if request.method != 'POST':
        return JsonResponse({'error': 'POST method required'}, status=405)

    try:
        import json
        data = json.loads(request.body)
        location = data.get('location', '').strip()

        if not location:
            return JsonResponse({'error': 'Location is required'}, status=400)

        # Get current organization
        from apps.organizations.middleware import get_current_organization
        current_org = get_current_organization(request)
        if not current_org:
            return JsonResponse({'error': 'Organization not found'}, status=400)

        # Get API settings
        try:
            api_settings = current_org.api_settings
            if not api_settings.has_weather_api:
                return JsonResponse({'error': 'Weather API not configured'}, status=400)
        except:
            return JsonResponse({'error': 'Weather API not configured'}, status=400)

        # Create weather service instance
        weather_service = WeatherService(
            api_key=api_settings.openweather_api_key,
            units=api_settings.weather_units
        )

        # Get weather data
        weather_data = weather_service.get_weather(location)

        if weather_data:
            return JsonResponse({
                'success': True,
                'weather_data': weather_data
            })
        else:
            return JsonResponse({'error': 'Weather data not available for this location'}, status=404)

    except json.JSONDecodeError:
        return JsonResponse({'error': 'Invalid JSON data'}, status=400)
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)
