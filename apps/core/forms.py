from django import forms
from django.contrib.auth.models import User
from django.core.exceptions import ValidationError
from .models import Client, Presenter, Industry
from apps.organizations.models import Organization, OrganizationMembership


class IndustryForm(forms.ModelForm):
    """Form for creating and editing industries"""

    class Meta:
        model = Industry
        fields = ['name', 'code', 'description', 'color', 'is_active']
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500',
                'placeholder': 'Enter industry name (e.g., Healthcare)'
            }),
            'code': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500',
                'placeholder': 'Enter industry code (e.g., healthcare)'
            }),
            'description': forms.Textarea(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500',
                'placeholder': 'Optional description of this industry...',
                'rows': 3
            }),
            'color': forms.TextInput(attrs={
                'type': 'color',
                'class': 'w-16 h-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500',
                'title': 'Choose industry color'
            }),
            'is_active': forms.CheckboxInput(attrs={
                'class': 'h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded'
            }),
        }

    def __init__(self, *args, **kwargs):
        self.organization = kwargs.pop('organization', None)
        super().__init__(*args, **kwargs)

    def clean_code(self):
        code = self.cleaned_data.get('code')
        if code:
            # Convert to lowercase and replace spaces with underscores
            code = code.lower().replace(' ', '_').replace('-', '_')
            # Check for uniqueness within organization
            if self.organization:
                existing = Industry.objects.filter(
                    organization=self.organization,
                    code=code
                ).exclude(pk=self.instance.pk if self.instance else None)
                if existing.exists():
                    raise ValidationError('An industry with this code already exists in your organization.')
        return code


class ClientForm(forms.ModelForm):
    """Form for creating and editing clients"""

    # Override industry field as ChoiceField to ensure proper rendering
    industry = forms.ChoiceField(
        choices=[],  # Will be populated in __init__
        required=False,
        widget=forms.Select(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500'
        })
    )

    def __init__(self, *args, **kwargs):
        self.organization = kwargs.pop('organization', None)
        super().__init__(*args, **kwargs)

        # Update industry choices to include custom industries
        if self.organization:
            self.fields['industry'].choices = Client.get_industry_choices(self.organization)
        else:
            self.fields['industry'].choices = [('', 'Select Industry')]

    class Meta:
        model = Client
        fields = ['name', 'contact_person', 'email', 'phone', 'address', 'industry', 'is_active']
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500',
                'placeholder': 'Enter client name'
            }),
            'contact_person': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500',
                'placeholder': 'Enter contact person name'
            }),
            'email': forms.EmailInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500',
                'placeholder': '<EMAIL>'
            }),
            'phone': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500',
                'placeholder': '+****************'
            }),
            'address': forms.Textarea(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500',
                'placeholder': 'Enter full address...',
                'rows': 3
            }),

            'is_active': forms.CheckboxInput(attrs={
                'class': 'h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded'
            }),
        }



