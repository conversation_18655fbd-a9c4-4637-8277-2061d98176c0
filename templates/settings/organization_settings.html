{% extends 'base.html' %}
{% load static %}

{% block title %}Organization Settings{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto">
    <!-- Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <a href="{% url 'settings:settings_dashboard' %}" class="text-gray-500 hover:text-gray-700 mr-4">
                        <i class="fa-solid fa-arrow-left"></i>
                    </a>
                    <h1 class="text-xl font-semibold text-gray-900">Organization Settings</h1>
                </div>
            </div>
        </div>
    </div>

    <!-- Settings Form -->
    <form method="post" class="space-y-6">
        {% csrf_token %}
        
        <!-- Time and Scheduling Settings -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Time & Scheduling</h3>
                <p class="text-sm text-gray-500 mt-1">Configure default timing and scheduling parameters</p>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="default_mention_duration" class="block text-sm font-medium text-gray-700 mb-1">Default Mention Duration (seconds)</label>
                        <input type="number" id="default_mention_duration" name="default_mention_duration" 
                               value="{{ settings.default_mention_duration }}" min="1" max="300"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                        <p class="text-xs text-gray-500 mt-1">Default duration for new mentions</p>
                    </div>
                    <div>
                        <label for="max_mentions_per_hour" class="block text-sm font-medium text-gray-700 mb-1">Max Mentions per Hour</label>
                        <input type="number" id="max_mentions_per_hour" name="max_mentions_per_hour" 
                               value="{{ settings.max_mentions_per_hour }}" min="1" max="20"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                        <p class="text-xs text-gray-500 mt-1">Maximum mentions allowed per hour per show</p>
                    </div>
                    <div>
                        <label for="min_gap_between_mentions" class="block text-sm font-medium text-gray-700 mb-1">Minimum Gap Between Mentions (seconds)</label>
                        <input type="number" id="min_gap_between_mentions" name="min_gap_between_mentions" 
                               value="{{ settings.min_gap_between_mentions }}" min="0" max="3600"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                        <p class="text-xs text-gray-500 mt-1">Minimum time gap required between mentions</p>
                    </div>
                    <div class="flex items-center">
                        <input type="checkbox" id="allow_overlapping_mentions" name="allow_overlapping_mentions" 
                               {% if settings.allow_overlapping_mentions %}checked{% endif %}
                               class="h-4 w-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500">
                        <label for="allow_overlapping_mentions" class="ml-2 block text-sm text-gray-900">
                            Allow Overlapping Mentions
                            <span class="text-gray-500 text-xs block">Permit mentions to overlap in time</span>
                        </label>
                    </div>
                </div>
            </div>
        </div>

        <!-- Conflict Detection Settings -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Conflict Detection</h3>
                <p class="text-sm text-gray-500 mt-1">Configure automatic conflict detection and resolution</p>
            </div>
            <div class="p-6">
                <div class="space-y-6">
                    <div class="flex items-center">
                        <input type="checkbox" id="enable_conflict_detection" name="enable_conflict_detection" 
                               {% if settings.enable_conflict_detection %}checked{% endif %}
                               class="h-4 w-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500">
                        <label for="enable_conflict_detection" class="ml-2 block text-sm text-gray-900">
                            Enable Conflict Detection
                            <span class="text-gray-500 text-xs block">Automatically detect scheduling conflicts</span>
                        </label>
                    </div>
                    
                    <div class="flex items-center">
                        <input type="checkbox" id="auto_resolve_conflicts" name="auto_resolve_conflicts" 
                               {% if settings.auto_resolve_conflicts %}checked{% endif %}
                               class="h-4 w-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500">
                        <label for="auto_resolve_conflicts" class="ml-2 block text-sm text-gray-900">
                            Auto-Resolve Conflicts
                            <span class="text-gray-500 text-xs block">Automatically resolve conflicts using the selected strategy</span>
                        </label>
                    </div>
                    
                    <div>
                        <label for="conflict_resolution_strategy" class="block text-sm font-medium text-gray-700 mb-1">Conflict Resolution Strategy</label>
                        <select id="conflict_resolution_strategy" name="conflict_resolution_strategy"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                            <option value="priority" {% if settings.conflict_resolution_strategy == 'priority' %}selected{% endif %}>By Priority</option>
                            <option value="first_come" {% if settings.conflict_resolution_strategy == 'first_come' %}selected{% endif %}>First Come First Serve</option>
                            <option value="manual" {% if settings.conflict_resolution_strategy == 'manual' %}selected{% endif %}>Manual Resolution</option>
                        </select>
                        <p class="text-xs text-gray-500 mt-1">How conflicts should be automatically resolved</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Notification Settings -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Notifications</h3>
                <p class="text-sm text-gray-500 mt-1">Configure system-wide notification preferences</p>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="flex items-center">
                        <input type="checkbox" id="enable_email_notifications" name="enable_email_notifications" 
                               {% if settings.enable_email_notifications %}checked{% endif %}
                               class="h-4 w-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500">
                        <label for="enable_email_notifications" class="ml-2 block text-sm text-gray-900">
                            Enable Email Notifications
                            <span class="text-gray-500 text-xs block">Send email notifications to users</span>
                        </label>
                    </div>
                    
                    <div class="flex items-center">
                        <input type="checkbox" id="enable_conflict_alerts" name="enable_conflict_alerts" 
                               {% if settings.enable_conflict_alerts %}checked{% endif %}
                               class="h-4 w-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500">
                        <label for="enable_conflict_alerts" class="ml-2 block text-sm text-gray-900">
                            Enable Conflict Alerts
                            <span class="text-gray-500 text-xs block">Alert users when conflicts are detected</span>
                        </label>
                    </div>
                    
                    <div class="flex items-center">
                        <input type="checkbox" id="enable_deadline_reminders" name="enable_deadline_reminders" 
                               {% if settings.enable_deadline_reminders %}checked{% endif %}
                               class="h-4 w-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500">
                        <label for="enable_deadline_reminders" class="ml-2 block text-sm text-gray-900">
                            Enable Deadline Reminders
                            <span class="text-gray-500 text-xs block">Send reminders before mention deadlines</span>
                        </label>
                    </div>
                    
                    <div>
                        <label for="reminder_hours_before" class="block text-sm font-medium text-gray-700 mb-1">Reminder Hours Before</label>
                        <input type="number" id="reminder_hours_before" name="reminder_hours_before" 
                               value="{{ settings.reminder_hours_before }}" min="1" max="168"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                        <p class="text-xs text-gray-500 mt-1">Hours before deadline to send reminders</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Approval Workflow Settings -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Approval Workflow</h3>
                <p class="text-sm text-gray-500 mt-1">Configure mention approval and workflow settings</p>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="flex items-center">
                        <input type="checkbox" id="require_mention_approval" name="require_mention_approval" 
                               {% if settings.require_mention_approval %}checked{% endif %}
                               class="h-4 w-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500">
                        <label for="require_mention_approval" class="ml-2 block text-sm text-gray-900">
                            Require Mention Approval
                            <span class="text-gray-500 text-xs block">All mentions must be approved before scheduling</span>
                        </label>
                    </div>
                    
                    <div class="flex items-center">
                        <input type="checkbox" id="auto_approve_recurring" name="auto_approve_recurring" 
                               {% if settings.auto_approve_recurring %}checked{% endif %}
                               class="h-4 w-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500">
                        <label for="auto_approve_recurring" class="ml-2 block text-sm text-gray-900">
                            Auto-Approve Recurring
                            <span class="text-gray-500 text-xs block">Automatically approve recurring mentions</span>
                        </label>
                    </div>
                    
                    <div>
                        <label for="approval_timeout_hours" class="block text-sm font-medium text-gray-700 mb-1">Approval Timeout (hours)</label>
                        <input type="number" id="approval_timeout_hours" name="approval_timeout_hours" 
                               value="{{ settings.approval_timeout_hours }}" min="1" max="168"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                        <p class="text-xs text-gray-500 mt-1">Hours before approval request times out</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Archive and Cleanup Settings -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Archive & Cleanup</h3>
                <p class="text-sm text-gray-500 mt-1">Configure automatic archiving and data cleanup</p>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="flex items-center">
                        <input type="checkbox" id="auto_archive_completed" name="auto_archive_completed" 
                               {% if settings.auto_archive_completed %}checked{% endif %}
                               class="h-4 w-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500">
                        <label for="auto_archive_completed" class="ml-2 block text-sm text-gray-900">
                            Auto-Archive Completed
                            <span class="text-gray-500 text-xs block">Automatically archive completed mentions</span>
                        </label>
                    </div>
                    
                    <div>
                        <label for="archive_after_days" class="block text-sm font-medium text-gray-700 mb-1">Archive After (days)</label>
                        <input type="number" id="archive_after_days" name="archive_after_days" 
                               value="{{ settings.archive_after_days }}" min="1" max="365"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                        <p class="text-xs text-gray-500 mt-1">Days after completion to archive mentions</p>
                    </div>
                    
                    <div>
                        <label for="delete_archived_after_days" class="block text-sm font-medium text-gray-700 mb-1">Delete Archived After (days)</label>
                        <input type="number" id="delete_archived_after_days" name="delete_archived_after_days" 
                               value="{{ settings.delete_archived_after_days }}" min="30" max="3650"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                        <p class="text-xs text-gray-500 mt-1">Days after archiving to permanently delete</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Form Actions -->
        <div class="flex items-center justify-end space-x-3 pt-6">
            <a href="{% url 'settings:settings_dashboard' %}" 
               class="px-4 py-2 border border-gray-300 text-gray-700 font-medium rounded-md hover:bg-gray-50">
                Cancel
            </a>
            <button type="submit" 
                    class="px-4 py-2 bg-primary-600 text-white font-medium rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2">
                Save Settings
            </button>
        </div>
    </form>
</div>
{% endblock %}
