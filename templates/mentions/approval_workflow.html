{% extends 'base.html' %}
{% load static %}

{% block title %}
  Mention Approval Workflow
{% endblock %}

{% block content %}
  {% csrf_token %}
  <div class="max-w-7xl mx-auto">
    <!-- Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-xl font-semibold text-gray-900">Mention Approval Workflow</h1>
            <p class="text-gray-600 mt-1">Review and approve radio mentions through multi-step process</p>
          </div>
          <div class="flex space-x-3">
            <button onclick="openFilterModal()" class="px-4 py-2 border border-gray-300 text-gray-700 font-medium rounded-md hover:bg-gray-50 flex items-center">
              <i class="fa-solid fa-filter mr-2"></i>
              Filters
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Workflow Status Overview -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600">Pending Review</p>
            <p class="text-2xl font-bold text-orange-600">{{ pending_count|default:0 }}</p>
          </div>
          <div class="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
            <i class="fa-solid fa-clock text-orange-600"></i>
          </div>
        </div>
      </div>
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600">In Review</p>
            <p class="text-2xl font-bold text-blue-600">{{ in_review_count|default:0 }}</p>
          </div>
          <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
            <i class="fa-solid fa-eye text-blue-600"></i>
          </div>
        </div>
      </div>
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600">Approved</p>
            <p class="text-2xl font-bold text-green-600">{{ approved_count|default:0 }}</p>
          </div>
          <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
            <i class="fa-solid fa-check text-green-600"></i>
          </div>
        </div>
      </div>
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600">Rejected</p>
            <p class="text-2xl font-bold text-red-600">{{ rejected_count|default:0 }}</p>
          </div>
          <div class="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center">
            <i class="fa-solid fa-times text-red-600"></i>
          </div>
        </div>
      </div>
    </div>

    <!-- Workflow Tabs -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
      <div class="border-b border-gray-200">
        <nav class="flex space-x-8 px-6">
          <button onclick="switchTab('pending')" id="pending-tab" class="py-4 text-sm font-medium text-primary-600 border-b-2 border-primary-600">Pending Review ({{ pending_count|default:0 }})</button>
          <button onclick="switchTab('review')" id="review-tab" class="py-4 text-sm font-medium text-gray-500 hover:text-gray-700">In Review ({{ in_review_count|default:0 }})</button>
          <button onclick="switchTab('approved')" id="approved-tab" class="py-4 text-sm font-medium text-gray-500 hover:text-gray-700">Approved ({{ approved_count|default:0 }})</button>
          <button onclick="switchTab('rejected')" id="rejected-tab" class="py-4 text-sm font-medium text-gray-500 hover:text-gray-700">Rejected ({{ rejected_count|default:0 }})</button>
        </nav>
      </div>

      <!-- Pending Review Tab Content -->
      <div id="pending-content" class="p-6">
        <div class="space-y-4">
          {% for mention in pending_mentions %}
            <div class="border border-gray-200 rounded-lg p-4 hover:shadow-sm transition-shadow">
              <div class="flex items-start justify-between mb-3">
                <div class="flex-1">
                  <div class="flex items-center mb-2">
                    <h4 class="font-semibold text-gray-900">{{ mention.title }}</h4>
                    <span class="ml-2 px-2 py-1 text-xs font-semibold rounded-full bg-orange-100 text-orange-800">Pending</span>
                    <span class="ml-2 px-2 py-1 text-xs font-semibold rounded-full {% if mention.priority == 3 or mention.priority == 4 %}
                        
                        
                        
                        
                        
                        
                        
                        
                        
                        
                        
                        
                        
                        
                        
                        
                        
                        bg-red-100 text-red-800

















                      {% elif mention.priority == 2 %}
                        
                        
                        
                        
                        
                        
                        
                        
                        
                        
                        
                        
                        
                        
                        
                        
                        
                        bg-blue-100 text-blue-800

















                      {% else %}
                        
                        
                        
                        
                        
                        
                        
                        
                        
                        
                        
                        
                        
                        
                        
                        
                        
                        bg-gray-100 text-gray-800

















                      {% endif %}">
                      {% if mention.priority == 4 %}
                        Urgent
                      {% elif mention.priority == 3 %}
                        High
                      {% elif mention.priority == 2 %}
                        Normal
                      {% else %}
                        Low
                      {% endif %}Priority
                    </span>
                  </div>
                  <p class="text-gray-600 text-sm mb-2">{{ mention.duration_seconds }}-second mention for {{ mention.client.name }}</p>
                  <div class="flex items-center text-sm text-gray-500">
                    <i class="fa-solid fa-building mr-1"></i>
                    {{ mention.client.name }}
                    <span class="mx-2">•</span>
                    <i class="fa-solid fa-calendar mr-1"></i>
                    Created: {{ mention.created_at|date:'M d, Y g:i A' }}
                  </div>
                </div>
                <div class="text-right text-sm text-gray-500">Submitted {{ mention.created_at|timesince }} ago</div>
              </div>

              <div class="bg-gray-50 rounded-md p-3 mb-3">
                <div class="text-sm font-medium text-gray-700 mb-1">Mention Content:</div>
                <p class="text-sm text-gray-600">{{ mention.content|truncatewords:30 }}</p>
              </div>

              {% if mention.notes %}
                <div class="bg-blue-50 rounded-md p-3 mb-3">
                  <div class="text-sm font-medium text-blue-700 mb-1">Notes:</div>
                  <p class="text-sm text-blue-600">{{ mention.notes }}</p>
                </div>
              {% endif %}

              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                  <div class="text-sm text-gray-500">
                    <i class="fa-solid fa-clock mr-1"></i>
                    Duration: {{ mention.duration_seconds }} seconds
                  </div>
                </div>
                <div class="flex space-x-2">
                  <button onclick="reviewMention({{ mention.pk }})" class="px-3 py-1 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700"><i class="fa-solid fa-eye mr-1"></i>Review</button>
                  <button onclick="quickApprove({{ mention.pk }})" class="px-3 py-1 bg-green-600 text-white text-sm font-medium rounded-md hover:bg-green-700"><i class="fa-solid fa-check mr-1"></i>Quick Approve</button>
                  <button onclick="rejectMention({{ mention.pk }})" class="px-3 py-1 bg-red-600 text-white text-sm font-medium rounded-md hover:bg-red-700"><i class="fa-solid fa-times mr-1"></i>Reject</button>
                </div>
              </div>
            </div>
          {% empty %}
            <div class="text-center py-12">
              <i class="fa-solid fa-clock text-gray-400 text-4xl mb-4"></i>
              <h3 class="text-lg font-medium text-gray-900 mb-2">No mentions pending review</h3>
              <p class="text-gray-500">When mentions are submitted, they'll appear here for approval.</p>
            </div>
          {% endfor %}
        </div>
      </div>

      <!-- In Review Tab Content -->
      <div id="review-content" class="p-6 hidden">
        <div class="space-y-4">
          {% for mention in in_review_mentions %}
            <div class="border border-gray-200 rounded-lg p-4 hover:shadow-sm transition-shadow">
              <div class="flex items-start justify-between mb-3">
                <div class="flex-1">
                  <div class="flex items-center mb-2">
                    <h4 class="font-semibold text-gray-900">{{ mention.title }}</h4>
                    <span class="ml-2 px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">In Review</span>
                    <span class="ml-2 px-2 py-1 text-xs font-semibold rounded-full
                      {% if mention.priority == 4 %}
                        
                        
                        
                        
                        
                        
                        
                        
                        
                        
                        
                        
                        
                        bg-red-100 text-red-800














                      {% elif mention.priority == 3 %}
                        
                        
                        
                        
                        
                        
                        
                        
                        
                        
                        
                        
                        
                        bg-orange-100 text-orange-800














                      {% elif mention.priority == 2 %}
                        
                        
                        
                        
                        
                        
                        
                        
                        
                        
                        
                        
                        
                        bg-yellow-100 text-yellow-800














                      {% else %}
                        
                        
                        
                        
                        
                        
                        
                        
                        
                        
                        
                        
                        
                        bg-green-100 text-green-800













                      {% endif %}">
                      {{ mention.priority_display }}
                    </span>
                  </div>
                  <p class="text-gray-600 text-sm mb-2">{{ mention.duration_seconds }}-second mention for {{ mention.client.name }}</p>
                  <div class="flex items-center text-sm text-gray-500">
                    <i class="fa-solid fa-building mr-1"></i>
                    {{ mention.client.name }}
                    <span class="mx-2">•</span>
                    <i class="fa-solid fa-calendar mr-1"></i>
                    Updated: {{ mention.updated_at|date:'M d, Y g:i A' }}
                  </div>
                </div>
                <div class="text-right text-sm text-gray-500">Updated {{ mention.updated_at|timesince }} ago</div>
              </div>
              <div class="bg-gray-50 rounded-md p-3 mb-3">
                <div class="text-sm font-medium text-gray-700 mb-1">Mention Content:</div>
                <p class="text-sm text-gray-600">{{ mention.content|truncatewords:30 }}</p>
              </div>
              <div class="flex items-center justify-between">
                <div class="text-sm text-gray-500">
                  <i class="fa-solid fa-clock mr-1"></i>
                  Duration: {{ mention.duration_seconds }} seconds
                </div>
                <div class="flex space-x-2">
                  <button onclick="quickApprove({{ mention.pk }})" class="px-3 py-1 bg-green-600 text-white text-sm font-medium rounded-md hover:bg-green-700"><i class="fa-solid fa-check mr-1"></i>Approve</button>
                  <button onclick="rejectMention({{ mention.pk }})" class="px-3 py-1 bg-red-600 text-white text-sm font-medium rounded-md hover:bg-red-700"><i class="fa-solid fa-times mr-1"></i>Reject</button>
                </div>
              </div>
            </div>
          {% empty %}
            <div class="text-center py-12">
              <i class="fa-solid fa-eye text-gray-400 text-4xl mb-4"></i>
              <p class="text-gray-500">No mentions currently in review</p>
            </div>
          {% endfor %}
        </div>
      </div>

      <!-- Approved Tab Content -->
      <div id="approved-content" class="p-6 hidden">
        <div class="space-y-4">
          {% for mention in approved_mentions %}
            <div class="border border-gray-200 rounded-lg p-4 hover:shadow-sm transition-shadow">
              <div class="flex items-start justify-between mb-3">
                <div class="flex-1">
                  <div class="flex items-center mb-2">
                    <h4 class="font-semibold text-gray-900">{{ mention.title }}</h4>
                    <span class="ml-2 px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">Approved</span>
                    <span class="ml-2 px-2 py-1 text-xs font-semibold rounded-full
                      {% if mention.priority == 4 %}
                        
                        
                        
                        
                        
                        
                        
                        
                        
                        
                        
                        
                        
                        bg-red-100 text-red-800














                      {% elif mention.priority == 3 %}
                        
                        
                        
                        
                        
                        
                        
                        
                        
                        
                        
                        
                        
                        bg-orange-100 text-orange-800














                      {% elif mention.priority == 2 %}
                        
                        
                        
                        
                        
                        
                        
                        
                        
                        
                        
                        
                        
                        bg-yellow-100 text-yellow-800














                      {% else %}
                        
                        
                        
                        
                        
                        
                        
                        
                        
                        
                        
                        
                        
                        bg-green-100 text-green-800













                      {% endif %}">
                      {{ mention.priority_display }}
                    </span>
                  </div>
                  <p class="text-gray-600 text-sm mb-2">{{ mention.duration_seconds }}-second mention for {{ mention.client.name }}</p>
                  <div class="flex items-center text-sm text-gray-500">
                    <i class="fa-solid fa-building mr-1"></i>
                    {{ mention.client.name }}
                    <span class="mx-2">•</span>
                    <i class="fa-solid fa-user mr-1"></i>
                    Approved by:{% if mention.approved_by %}
                      {{ mention.approved_by.get_full_name|default:mention.approved_by.username }}
                    {% else %}
                      System
                    {% endif %}
                    <span class="mx-2">•</span>
                    <i class="fa-solid fa-calendar mr-1"></i>
                    {% if mention.approved_at %}
                      {{ mention.approved_at|date:'M d, Y g:i A' }}
                    {% else %}
                      {{ mention.updated_at|date:'M d, Y g:i A' }}
                    {% endif %}
                  </div>
                </div>
                <div class="text-right text-sm text-gray-500">
                  Approved{% if mention.approved_at %}
                    {{ mention.approved_at|timesince }}
                  {% else %}
                    {{ mention.updated_at|timesince }}
                  {% endif %}ago
                </div>
              </div>
              <div class="bg-gray-50 rounded-md p-3 mb-3">
                <div class="text-sm font-medium text-gray-700 mb-1">Mention Content:</div>
                <p class="text-sm text-gray-600">{{ mention.content|truncatewords:30 }}</p>
              </div>
              <div class="flex items-center justify-between">
                <div class="text-sm text-gray-500">
                  <i class="fa-solid fa-clock mr-1"></i>
                  Duration: {{ mention.duration_seconds }} seconds
                </div>
                <div class="flex space-x-2">
                  <a href="{% url 'mentions:mention_detail' mention.pk %}" class="px-3 py-1 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700"><i class="fa-solid fa-eye mr-1"></i>View Details</a>
                </div>
              </div>
            </div>
          {% empty %}
            <div class="text-center py-12">
              <i class="fa-solid fa-check-circle text-gray-400 text-4xl mb-4"></i>
              <p class="text-gray-500">No approved mentions yet</p>
            </div>
          {% endfor %}
        </div>
      </div>

      <!-- Rejected Tab Content -->
      <div id="rejected-content" class="p-6 hidden">
        <div class="space-y-4">
          {% for mention in rejected_mentions %}
            <div class="border border-gray-200 rounded-lg p-4 hover:shadow-sm transition-shadow">
              <div class="flex items-start justify-between mb-3">
                <div class="flex-1">
                  <div class="flex items-center mb-2">
                    <h4 class="font-semibold text-gray-900">{{ mention.title }}</h4>
                    <span class="ml-2 px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">Rejected</span>
                    <span class="ml-2 px-2 py-1 text-xs font-semibold rounded-full
                      {% if mention.priority == 4 %}
                        
                        
                        
                        
                        
                        
                        
                        
                        
                        
                        
                        
                        
                        bg-red-100 text-red-800














                      {% elif mention.priority == 3 %}
                        
                        
                        
                        
                        
                        
                        
                        
                        
                        
                        
                        
                        
                        bg-orange-100 text-orange-800














                      {% elif mention.priority == 2 %}
                        
                        
                        
                        
                        
                        
                        
                        
                        
                        
                        
                        
                        
                        bg-yellow-100 text-yellow-800














                      {% else %}
                        
                        
                        
                        
                        
                        
                        
                        
                        
                        
                        
                        
                        
                        bg-green-100 text-green-800













                      {% endif %}">
                      {{ mention.priority_display }}
                    </span>
                  </div>
                  <p class="text-gray-600 text-sm mb-2">{{ mention.duration_seconds }}-second mention for {{ mention.client.name }}</p>
                  <div class="flex items-center text-sm text-gray-500">
                    <i class="fa-solid fa-building mr-1"></i>
                    {{ mention.client.name }}
                    <span class="mx-2">•</span>
                    <i class="fa-solid fa-calendar mr-1"></i>
                    Rejected: {{ mention.updated_at|date:'M d, Y g:i A' }}
                  </div>
                </div>
                <div class="text-right text-sm text-gray-500">Rejected {{ mention.updated_at|timesince }} ago</div>
              </div>
              <div class="bg-gray-50 rounded-md p-3 mb-3">
                <div class="text-sm font-medium text-gray-700 mb-1">Mention Content:</div>
                <p class="text-sm text-gray-600">{{ mention.content|truncatewords:30 }}</p>
              </div>
              {% if mention.notes %}
                <div class="bg-red-50 rounded-md p-3 mb-3">
                  <div class="text-sm font-medium text-red-700 mb-1">Rejection Reason:</div>
                  <p class="text-sm text-red-600">{{ mention.notes }}</p>
                </div>
              {% endif %}
              <div class="flex items-center justify-between">
                <div class="text-sm text-gray-500">
                  <i class="fa-solid fa-clock mr-1"></i>
                  Duration: {{ mention.duration_seconds }} seconds
                </div>
                <div class="flex space-x-2">
                  <a href="{% url 'mentions:mention_detail' mention.pk %}" class="px-3 py-1 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700"><i class="fa-solid fa-eye mr-1"></i>View Details</a>
                </div>
              </div>
            </div>
          {% empty %}
            <div class="text-center py-12">
              <i class="fa-solid fa-times-circle text-gray-400 text-4xl mb-4"></i>
              <p class="text-gray-500">No rejected mentions</p>
            </div>
          {% endfor %}
        </div>
      </div>
    </div>
  </div>

  <!-- Review Modal -->
  <div id="reviewModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
    <div class="relative top-20 mx-auto p-5 border w-2xl max-w-2xl shadow-lg rounded-md bg-white">
      <div class="mt-3">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Review Mention</h3>
        <div id="reviewContent">
          <!-- Content will be populated by JavaScript -->
        </div>
        <div class="flex justify-end space-x-3 mt-6">
          <button onclick="closeReviewModal()" class="px-4 py-2 bg-gray-300 text-gray-800 text-sm font-medium rounded-md hover:bg-gray-400">Close</button>
          <button onclick="approveMentionFromModal()" class="px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-md hover:bg-green-700">Approve</button>
          <button onclick="rejectMentionFromModal()" class="px-4 py-2 bg-red-600 text-white text-sm font-medium rounded-md hover:bg-red-700">Reject</button>
        </div>
      </div>
    </div>
  </div>

  <!-- Rejection Modal -->
  <div id="rejectModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
      <div class="mt-3">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Reject Mention</h3>
        <textarea id="rejectReason" placeholder="Reason for rejection..." class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500" rows="4"></textarea>
        <div class="flex justify-end space-x-3 mt-4">
          <button onclick="closeRejectModal()" class="px-4 py-2 bg-gray-300 text-gray-800 text-sm font-medium rounded-md hover:bg-gray-400">Cancel</button>
          <button onclick="confirmReject()" class="px-4 py-2 bg-red-600 text-white text-sm font-medium rounded-md hover:bg-red-700">Reject</button>
        </div>
      </div>
    </div>
  </div>

  <!-- Filter Modal -->
  <div id="filterModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
      <div class="mt-3">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Filter Mentions</h3>
        <form id="filterForm">
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Client</label>
              <select class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                <option value="">All Clients</option>
                {% for client in clients %}
                  <option value="{{ client.pk }}">{{ client.name }}</option>
                {% endfor %}
              </select>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Priority</label>
              <select class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                <option value="">All Priorities</option>
                <option value="4">Urgent</option>
                <option value="3">High</option>
                <option value="2">Normal</option>
                <option value="1">Low</option>
              </select>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Date Range</label>
              <div class="grid grid-cols-2 gap-2">
                <input type="date" class="px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500" />
                <input type="date" class="px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500" />
              </div>
            </div>
          </div>
          <div class="flex justify-end space-x-3 mt-6">
            <button type="button" onclick="closeFilterModal()" class="px-4 py-2 bg-gray-300 text-gray-800 text-sm font-medium rounded-md hover:bg-gray-400">Cancel</button>
            <button type="submit" class="px-4 py-2 bg-primary-600 text-white text-sm font-medium rounded-md hover:bg-primary-700">Apply Filters</button>
          </div>
        </form>
      </div>
    </div>
  </div>
{% endblock %}

{% block extra_js %}
  <script>
    let currentMentionId = null
    
    // Helper function to get cookie value
    function getCookie(name) {
      let cookieValue = null
      if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';')
        for (let i = 0; i < cookies.length; i++) {
          const cookie = cookies[i].trim()
          if (cookie.substring(0, name.length + 1) === name + '=') {
            cookieValue = decodeURIComponent(cookie.substring(name.length + 1))
            break
          }
        }
      }
      return cookieValue
    }
    
    function switchTab(tab) {
      // Hide all tab contents
      document.getElementById('pending-content').classList.add('hidden')
      document.getElementById('review-content').classList.add('hidden')
      document.getElementById('approved-content').classList.add('hidden')
      document.getElementById('rejected-content').classList.add('hidden')
    
      // Remove active styling from all tabs
      document.querySelectorAll('nav button').forEach((btn) => {
        btn.className = 'py-4 text-sm font-medium text-gray-500 hover:text-gray-700'
      })
    
      // Show selected tab content
      document.getElementById(tab + '-content').classList.remove('hidden')
    
      // Add active styling to selected tab
      document.getElementById(tab + '-tab').className = 'py-4 text-sm font-medium text-primary-600 border-b-2 border-primary-600'
    }
    
    function reviewMention(mentionId) {
      currentMentionId = mentionId
      // In a real app, you would fetch the mention details via AJAX
      document.getElementById('reviewContent').innerHTML = `
                                                                                <div class="space-y-4">
                                                                                    <div>
                                                                                        <h4 class="font-medium text-gray-900">Mention Details</h4>
                                                                                        <p class="text-sm text-gray-600">Loading mention details for ID: ${mentionId}</p>
                                                                                    </div>
                                                                                </div>
                                                                            `
      document.getElementById('reviewModal').classList.remove('hidden')
    }
    
    function closeReviewModal() {
      document.getElementById('reviewModal').classList.add('hidden')
      currentMentionId = null
    }
    
    function quickApprove(mentionId) {
      if (confirm('Are you sure you want to approve this mention?')) {
        // Get CSRF token
        const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]')?.value || document.querySelector('meta[name=csrf-token]')?.getAttribute('content') || getCookie('csrftoken')
    
        // Make AJAX call to approve mention
        fetch(`/mentions/${mentionId}/approve/`, {
          method: 'POST',
          headers: {
            'X-CSRFToken': csrfToken,
            'Content-Type': 'application/json'
          }
        })
          .then((response) => response.json())
          .then((data) => {
            if (data.success) {
              location.reload()
            } else {
              alert('Error approving mention: ' + (data.error || 'Unknown error'))
            }
          })
          .catch((error) => {
            console.error('Error:', error)
            alert('Network error occurred while approving mention')
          })
      }
    }
    
    function rejectMention(mentionId) {
      currentMentionId = mentionId
      document.getElementById('rejectModal').classList.remove('hidden')
    }
    
    function closeRejectModal() {
      document.getElementById('rejectModal').classList.add('hidden')
      document.getElementById('rejectReason').value = ''
      currentMentionId = null
    }
    
    function confirmReject() {
      const reason = document.getElementById('rejectReason').value
      if (!reason.trim()) {
        alert('Please provide a reason for rejection')
        return
      }
    
      // Get CSRF token
      const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]')?.value || document.querySelector('meta[name=csrf-token]')?.getAttribute('content') || getCookie('csrftoken')
    
      fetch(`/mentions/${currentMentionId}/reject/`, {
        method: 'POST',
        headers: {
          'X-CSRFToken': csrfToken,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ reason: reason })
      })
        .then((response) => response.json())
        .then((data) => {
          if (data.success) {
            closeRejectModal()
            location.reload()
          } else {
            alert('Error rejecting mention: ' + (data.error || 'Unknown error'))
          }
        })
        .catch((error) => {
          console.error('Error:', error)
          alert('Network error occurred while rejecting mention')
        })
    }
    
    function approveMentionFromModal() {
      quickApprove(currentMentionId)
      closeReviewModal()
    }
    
    function rejectMentionFromModal() {
      closeReviewModal()
      rejectMention(currentMentionId)
    }
    
    function openFilterModal() {
      document.getElementById('filterModal').classList.remove('hidden')
    }
    
    function closeFilterModal() {
      document.getElementById('filterModal').classList.add('hidden')
    }
    
    // Filter form submission
    document.getElementById('filterForm').addEventListener('submit', function (e) {
      e.preventDefault()
      // Apply filters - in a real app, this would update the URL and reload the page
      alert('Filters applied!')
      closeFilterModal()
    })
  </script>
{% endblock %}
